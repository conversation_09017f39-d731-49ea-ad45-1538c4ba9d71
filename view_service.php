<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if service ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID de service invalide.";
    header("Location: browse_services.php");
    exit;
}

$service_id = intval($_GET['id']);

// Get service data
$stmt = $conn->prepare("
    SELECT s.*, u.username as freelancer_username, u.full_name as freelancer_name, u.id as freelancer_id
    FROM services s
    JOIN users u ON s.user_id = u.id
    WHERE s.id = ? AND s.status = 'active'
");
$stmt->execute([$service_id]);
$service = $stmt->fetch();

if (!$service) {
    $_SESSION['error'] = "Service introuvable ou inactif.";
    header("Location: browse_services.php");
    exit;
}

// Get freelancer data
$stmt = $conn->prepare("
    SELECT u.*, COUNT(p.id) as completed_projects, 
           (SELECT AVG(r.rating) FROM reviews r WHERE r.reviewee_id = u.id) as avg_rating,
           (SELECT COUNT(*) FROM reviews r WHERE r.reviewee_id = u.id) as rating_count
    FROM users u
    LEFT JOIN projects p ON u.id = p.freelancer_id AND p.status = 'completed'
    WHERE u.id = ?
    GROUP BY u.id
");
$stmt->execute([$service['freelancer_id']]);
$freelancer = $stmt->fetch();

// Check if user is pro
$is_pro = isUserPro($freelancer['id']);

// Get recent reviews for the freelancer
$stmt = $conn->prepare("
    SELECT r.*, p.title as project_title, u.username as reviewer_name
    FROM reviews r
    JOIN projects p ON r.project_id = p.id
    JOIN users u ON r.reviewer_id = u.id
    WHERE r.reviewee_id = ?
    ORDER BY r.created_at DESC
    LIMIT 2
");
$stmt->execute([$service['freelancer_id']]);
$reviews = $stmt->fetchAll();

// Get similar services
$stmt = $conn->prepare("
    SELECT s.*, u.username as freelancer_username
    FROM services s
    JOIN users u ON s.user_id = u.id
    WHERE s.category = ? AND s.id != ? AND s.status = 'active'
    ORDER BY RAND()
    LIMIT 3
");
$stmt->execute([$service['category'], $service_id]);
$similar_services = $stmt->fetchAll();

// Process booking request
$booking_error = '';
$booking_success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['book_service'])) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['error'] = "Vous devez être connecté pour réserver un service.";
        header("Location: login.php");
        exit;
    }
    
    // Check if user is a client
    if ($_SESSION['user_type'] !== 'client') {
        $booking_error = "Seuls les clients peuvent réserver des services.";
    }
    // Check if user is trying to book their own service
    elseif ($_SESSION['user_id'] === $service['freelancer_id']) {
        $booking_error = "Vous ne pouvez pas réserver votre propre service.";
    }
    else {
        $client_id = $_SESSION['user_id'];
        $freelancer_id = $service['freelancer_id'];
        $project_title = sanitize($_POST['project_title'] ?? '');
        $project_description = sanitize($_POST['project_description'] ?? '');
        $price = floatval($service['price']);
        
        if (empty($project_title) || empty($project_description)) {
            $booking_error = "Veuillez remplir tous les champs requis.";
        } else {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO projects (title, description, client_id, freelancer_id, service_id, price, status)
                    VALUES (?, ?, ?, ?, ?, ?, 'pending')
                ");
                $stmt->execute([$project_title, $project_description, $client_id, $freelancer_id, $service_id, $price]);
                
                $booking_success = "Votre demande a été envoyée au freelancer. Vous serez notifié une fois qu'il aura accepté le projet.";
            } catch (PDOException $e) {
                $booking_error = "Erreur lors de la réservation du service: " . $e->getMessage();
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8 mt-20">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Service Header -->
        <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?= htmlspecialchars($service['title']) ?></h1>
            
            <div class="flex flex-wrap items-center text-sm text-gray-600 mb-4">
                <span class="mr-4">
                    <i class="ri-folder-line mr-1"></i> <?= htmlspecialchars($service['category']) ?>
                </span>
                <span>
                    <i class="ri-time-line mr-1"></i> Mis à jour le <?= date('d/m/Y', strtotime($service['updated_at'])) ?>
                </span>
            </div>
            
            <!-- Freelancer info -->
            <div class="flex items-center mt-4">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 font-bold mr-3">
                    <?= strtoupper(substr($freelancer['username'], 0, 1)) ?>
                </div>
                <div>
                    <a href="profile.php?id=<?= $freelancer['id'] ?>" class="font-medium text-indigo-600 hover:text-indigo-800">
                        <?= htmlspecialchars($freelancer['full_name']) ?>
                        <?php if ($is_pro): ?>
                            <span class="bg-yellow-400 text-xs font-semibold px-2 py-1 rounded-full ml-2">PRO</span>
                        <?php endif; ?>
                    </a>
                    <div class="flex items-center">
                        <div class="text-yellow-400">
                            <?php 
                            $avg_rating = $freelancer['avg_rating'] ? round($freelancer['avg_rating'], 1) : 0;
                            for ($i = 1; $i <= 5; $i++) {
                                echo $i <= floor($avg_rating) ? '★' : '☆';
                            }
                            ?>
                        </div>
                        <span class="ml-1 text-sm text-gray-600">
                            (<?= $freelancer['rating_count'] ?? 0 ?> avis)
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Service Content -->
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Left Column - Service Details -->
                <div class="md:col-span-2">
                    <h2 class="text-xl font-bold mb-4">Description du service</h2>
                    <div class="prose max-w-none">
                        <?= nl2br(htmlspecialchars($service['description'])) ?>
                    </div>
                    
                    <!-- Reviews Section -->
                    <?php if (!empty($reviews)): ?>
                    <div class="mt-12">
                        <h2 class="text-xl font-bold mb-4">Avis récents sur <?= htmlspecialchars($freelancer['username']) ?></h2>
                        
                        <div class="space-y-6">
                            <?php foreach ($reviews as $review): ?>
                            <div class="border-l-4 border-indigo-200 pl-4 py-2">
                                <div class="flex justify-between">
                                    <p class="font-medium"><?= htmlspecialchars($review['project_title']) ?></p>
                                    <div class="text-yellow-400">
                                        <?php 
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $review['rating'] ? '★' : '☆';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <p class="italic text-gray-700 mt-1">"<?= htmlspecialchars($review['comment']) ?>"</p>
                                <p class="text-sm text-gray-500 mt-1">- <?= htmlspecialchars($review['reviewer_name']) ?></p>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-4">
                            <a href="profile.php?id=<?= $freelancer['id'] ?>" class="text-indigo-600 hover:underline">
                                Voir tous les avis →
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Right Column - Pricing and Booking -->
                <div>
                    <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4"><?= number_format($service['price'], 2) ?> DH</h3>
                        
                        <?php if (isset($_SESSION['user_id']) && $_SESSION['user_type'] === 'client' && $_SESSION['user_id'] !== $service['freelancer_id']): ?>
                            <!-- Booking Form -->
                            <?php if (!empty($booking_error)): ?>
                                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                                    <p><?= $booking_error ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($booking_success)): ?>
                                <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                                    <p><?= $booking_success ?></p>
                                </div>
                            <?php else: ?>
                                <form method="POST" action="view_service.php?id=<?= $service_id ?>">
                                    <div class="mb-4">
                                        <label for="project_title" class="block text-sm font-medium text-gray-700 mb-1">Titre du projet</label>
                                        <input type="text" id="project_title" name="project_title" class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="project_description" class="block text-sm font-medium text-gray-700 mb-1">Description du projet</label>
                                        <textarea id="project_description" name="project_description" rows="4" class="w-full px-3 py-2 border rounded-md" required></textarea>
                                    </div>
                                    
                                    <button type="submit" name="book_service" class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition">
                                        Réserver ce service
                                    </button>
                                </form>
                            <?php endif; ?>
                        <?php elseif (isset($_SESSION['user_id']) && $_SESSION['user_id'] === $service['freelancer_id']): ?>
                            <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4" role="alert">
                                <p>C'est votre propre service. Vous pouvez le <a href="edit_service.php?id=<?= $service_id ?>" class="underline">modifier</a> si nécessaire.</p>
                            </div>
                        <?php else: ?>
                            <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4" role="alert">
                                <p><a href="login.php" class="underline">Connectez-vous</a> en tant que client pour réserver ce service.</p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Freelancer Stats -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold mb-4">Statistiques du freelancer</h4>
                            <ul class="space-y-2">
                                <li class="flex justify-between">
                                    <span class="text-gray-600">Projets complétés</span>
                                    <span class="font-medium"><?= $freelancer['completed_projects'] ?? 0 ?></span>
                                </li>
                                <li class="flex justify-between">
                                    <span class="text-gray-600">Note moyenne</span>
                                    <span class="font-medium"><?= number_format($avg_rating, 1) ?>/5</span>
                                </li>
                                <li class="flex justify-between">
                                    <span class="text-gray-600">Membre depuis</span>
                                    <span class="font-medium"><?= date('M Y', strtotime($freelancer['created_at'])) ?></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Similar Services -->
    <?php if (!empty($similar_services)): ?>
    <div class="mt-12">
        <h2 class="text-2xl font-bold mb-6">Services similaires</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <?php foreach ($similar_services as $similar): ?>
            <div class="service-card">
                <div class="service-card-header">
                    <h3><?= htmlspecialchars($similar['title']) ?></h3>
                    <p class="text-sm text-gray-500">Par: <?= htmlspecialchars($similar['freelancer_username']) ?></p>
                </div>
                <div class="service-card-body">
                    <p><?= nl2br(htmlspecialchars(substr($similar['description'], 0, 100) . (strlen($similar['description']) > 100 ? '...' : ''))) ?></p>
                </div>
                <div class="service-card-footer">
                    <span class="service-price"><?= number_format($similar['price'], 2) ?> DH</span>
                    <a href="view_service.php?id=<?= $similar['id'] ?>" class="btn btn-primary">Voir détails</a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
