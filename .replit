modules = ["php", "php-8.2"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "PHP Server"

[[workflows.workflow]]
name = "PHP Server"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "php -S 0.0.0.0:5000"
waitForPort = 5000

[deployment]
run = ["sh", "-c", "php -S 0.0.0.0:5000"]

[[ports]]
localPort = 5000
externalPort = 80
