<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Check if service ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID de service invalide.";
    header("Location: freelancer_dashboard.php");
    exit;
}

$service_id = intval($_GET['id']);

// Check if service exists and belongs to the user
$stmt = $conn->prepare("SELECT * FROM services WHERE id = ? AND user_id = ?");
$stmt->execute([$service_id, $user_id]);
$service = $stmt->fetch();

if (!$service) {
    $_SESSION['error'] = "Service introuvable ou vous n'avez pas les droits pour le modifier.";
    header("Location: freelancer_dashboard.php");
    exit;
}

// Process form submission
$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = sanitize($_POST['title'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $category = sanitize($_POST['category'] ?? '');
    $status = sanitize($_POST['status'] ?? 'active');
    
    if (empty($title)) {
        $error = "Le titre est requis.";
    } elseif (empty($description)) {
        $error = "La description est requise.";
    } elseif ($price <= 0) {
        $error = "Le prix doit être supérieur à 0.";
    } elseif (empty($category)) {
        $error = "La catégorie est requise.";
    } else {
        try {
            $stmt = $conn->prepare("UPDATE services SET title = ?, description = ?, price = ?, category = ?, status = ? WHERE id = ? AND user_id = ?");
            $stmt->execute([$title, $description, $price, $category, $status, $service_id, $user_id]);
            
            $_SESSION['success'] = "Service mis à jour avec succès!";
            header("Location: freelancer_dashboard.php");
            exit;
        } catch (PDOException $e) {
            $error = "Erreur lors de la mise à jour du service: " . $e->getMessage();
        }
    }
}

// Get all categories
$categories = [];
$stmt = $conn->query("SELECT name FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

include 'includes/header.php';
?>

<div class="form-container">
    <h2>Modifier le Service</h2>
    
    <?php if (!empty($error)): ?>
        <div class="error-message" style="background-color: #fee; color: #e53e3e; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success)): ?>
        <div class="success-message" style="background-color: #f0fff4; color: #38a169; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $success; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="edit_service.php?id=<?= $service_id ?>">
        <div class="form-group">
            <label for="title">Titre du service</label>
            <input type="text" id="title" name="title" value="<?= htmlspecialchars($service['title']) ?>" required>
        </div>
        
        <div class="form-group">
            <label for="category">Catégorie</label>
            <select id="category" name="category" required>
                <option value="">Sélectionnez une catégorie</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= htmlspecialchars($category) ?>" <?= ($service['category'] === $category) ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="6" required><?= htmlspecialchars($service['description']) ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="price">Prix (DH)</label>
            <input type="number" id="price" name="price" step="0.01" value="<?= htmlspecialchars($service['price']) ?>" required>
        </div>
        
        <div class="form-group">
            <label for="status">Statut</label>
            <select id="status" name="status" required>
                <option value="active" <?= ($service['status'] === 'active') ? 'selected' : '' ?>>Actif</option>
                <option value="inactive" <?= ($service['status'] === 'inactive') ? 'selected' : '' ?>>Inactif</option>
                <option value="pending" <?= ($service['status'] === 'pending') ? 'selected' : '' ?>>En attente</option>
            </select>
        </div>
        
        <div class="form-buttons">
            <a href="freelancer_dashboard.php" class="btn btn-secondary">Annuler</a>
            <button type="submit" class="btn btn-primary">Mettre à jour</button>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
