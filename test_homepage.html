<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Homepage - SawbLi</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
        }

        /* Modern Hero Section */
        .hero-modern {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            overflow: hidden;
            padding: 80px 0;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 25%, #f9fafb 50%, #eff6ff 75%, #dbeafe 100%);
        }

        .hero-content-modern {
            display: grid;
            grid-template-columns: 1fr;
            gap: 48px;
            align-items: center;
            position: relative;
            z-index: 1;
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 16px;
        }

        @media (min-width: 1024px) {
            .hero-content-modern {
                grid-template-columns: 1fr 1fr;
                gap: 64px;
                padding: 0 32px;
            }
        }

        .hero-text {
            text-align: center;
        }

        @media (min-width: 1024px) {
            .hero-text {
                text-align: left;
            }
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #dbeafe;
            color: #1d4ed8;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 24px;
        }

        .hero-title-modern {
            font-size: 36px;
            font-weight: 700;
            line-height: 1.25;
            color: #111827;
            margin-bottom: 24px;
        }

        @media (min-width: 768px) {
            .hero-title-modern {
                font-size: 48px;
            }
        }

        @media (min-width: 1024px) {
            .hero-title-modern {
                font-size: 60px;
            }
        }

        .text-gradient {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description-modern {
            font-size: 18px;
            line-height: 1.625;
            color: #4b5563;
            margin-bottom: 32px;
            max-width: 600px;
        }

        .hero-stats {
            display: flex;
            gap: 32px;
            justify-content: center;
            margin-bottom: 32px;
        }

        @media (min-width: 1024px) {
            .hero-stats {
                justify-content: flex-start;
            }
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            line-height: 1;
        }

        .stat-label {
            display: block;
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        .hero-actions-modern {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        @media (min-width: 1024px) {
            .hero-actions-modern {
                justify-content: flex-start;
            }
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            border: 1px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.15s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background-color: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .btn-outline {
            background-color: transparent;
            color: #2563eb;
            border-color: #2563eb;
        }

        .btn-outline:hover {
            background-color: #eff6ff;
            color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-video-modern {
            position: relative;
            width: 100%;
            max-width: 600px;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .hero-video-modern video {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Services Section */
        .services-modern {
            padding: 80px 0;
            background: #f9fafb;
        }

        .container {
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 16px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 64px;
        }

        .section-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #dbeafe;
            color: #1d4ed8;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 36px;
            font-weight: 700;
            line-height: 1.25;
            color: #111827;
            margin-bottom: 16px;
        }

        .section-description {
            font-size: 18px;
            color: #4b5563;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 64px;
        }

        .service-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            position: relative;
            overflow: hidden;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            border-color: #bfdbfe;
        }

        .service-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 24px;
            background: #dbeafe;
            color: #2563eb;
        }

        .service-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .service-description {
            font-size: 16px;
            color: #4b5563;
            line-height: 1.625;
            margin-bottom: 16px;
        }

        .service-arrow {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: #2563eb;
            font-size: 18px;
            transition: transform 0.15s ease;
        }

        .service-card:hover .service-arrow {
            transform: translateX(4px);
        }
    </style>
</head>
<body>
    <!-- Modern Hero Section -->
    <section class="hero-modern">
        <div class="hero-content-modern">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="ri-star-fill"></i>
                    <span>Plateforme #1 au Maroc</span>
                </div>
                
                <h1 class="hero-title-modern">
                    Connectez-vous avec les 
                    <span class="text-gradient">meilleurs freelances</span> 
                    marocains
                </h1>
                
                <p class="hero-description-modern">
                    Découvrez des talents exceptionnels dans le développement web, marketing digital, 
                    design graphique et bien plus. Réalisez vos projets avec des professionnels qualifiés.
                </p>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Freelances</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Projets réalisés</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.9/5</span>
                        <span class="stat-label">Satisfaction</span>
                    </div>
                </div>
                
                <div class="hero-actions-modern">
                    <a href="#" class="btn btn-primary">
                        <i class="ri-user-star-line"></i>
                        <span>Devenir freelance</span>
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="ri-search-line"></i>
                        <span>Trouver un service</span>
                    </a>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="hero-video-modern">
                    <video autoplay loop muted playsinline>
                        <source src="IMG/SawbLi.mp4" type="video/mp4">
                    </video>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern Services Section -->
    <section class="services-modern">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">
                    <i class="ri-service-line"></i>
                    <span>Nos Services</span>
                </div>
                <h2 class="section-title">
                    Explorez nos <span class="text-gradient">catégories de services</span>
                </h2>
                <p class="section-description">
                    Découvrez une large gamme de services professionnels proposés par nos freelances experts
                </p>
            </div>
            
            <div class="services-grid">
                <a href="#" class="service-card">
                    <div class="service-icon">
                        <i class="ri-code-line"></i>
                    </div>
                    <h3 class="service-title">Développement</h3>
                    <p class="service-description">Découvrez nos experts en développement</p>
                    <div class="service-arrow">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
                
                <a href="#" class="service-card">
                    <div class="service-icon">
                        <i class="ri-palette-line"></i>
                    </div>
                    <h3 class="service-title">Design Graphique</h3>
                    <p class="service-description">Découvrez nos experts en design graphique</p>
                    <div class="service-arrow">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
                
                <a href="#" class="service-card">
                    <div class="service-icon">
                        <i class="ri-megaphone-line"></i>
                    </div>
                    <h3 class="service-title">Marketing Digital</h3>
                    <p class="service-description">Découvrez nos experts en marketing digital</p>
                    <div class="service-arrow">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
            </div>
        </div>
    </section>
</body>
</html>
