<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Vérifier si l'utilisateur est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

// Vérifier si l'ID du service est passé
if (isset($_POST['service_id'])) {
    $service_id = $_POST['service_id'];

    // Mettre à jour la colonne 'approved' du service
    $stmt = $conn->prepare("UPDATE services SET approved = 1 WHERE id = :service_id");
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();

    // Redirection vers la page des services
    header("Location: admin_services.php"); // Rediriger vers la page des services une fois approuvé
    exit;
}
?>
