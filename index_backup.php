<?php
include 'db.php';
$conn = $pdo;
include 'includes/header.php';
?>

<!-- Modern Hero Section -->
<section class="hero-modern">
    <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
    </div>

    <div class="container">
        <div class="hero-content-modern">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="ri-star-fill"></i>
                    <span>Plateforme #1 au Maroc</span>
                </div>

                <h1 class="hero-title-modern">
                    Connectez-vous avec les
                    <span class="text-gradient">meilleurs freelances</span>
                    marocains
                </h1>

                <p class="hero-description-modern">
                    Découvrez des talents exceptionnels dans le développement web, marketing digital,
                    design graphique et bien plus. Réalisez vos projets avec des professionnels qualifiés.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Freelances</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Projets réalisés</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.9/5</span>
                        <span class="stat-label">Satisfaction</span>
                    </div>
                </div>

                <div class="hero-actions-modern">
                    <a href="signup.php?type=freelancer" class="btn btn-primary btn-lg">
                        <i class="ri-user-star-line"></i>
                        <span>Devenir freelance</span>
                    </a>
                    <a href="browse_services.php" class="btn btn-outline btn-lg">
                        <i class="ri-search-line"></i>
                        <span>Trouver un service</span>
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="hero-video-modern">
                    <video src="IMG/SawbLi.mp4" autoplay loop muted playsinline>
                        <source src="IMG/SawbLi.mp4" type="video/mp4">
                    </video>
                    <div class="video-overlay"></div>
                </div>

                <div class="floating-cards">
                    <div class="floating-card card-1">
                        <i class="ri-code-line"></i>
                        <span>Développement</span>
                    </div>
                    <div class="floating-card card-2">
                        <i class="ri-palette-line"></i>
                        <span>Design</span>
                    </div>
                    <div class="floating-card card-3">
                        <i class="ri-megaphone-line"></i>
                        <span>Marketing</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* ========================================
   MODERN HOMEPAGE STYLES - DIRECT VALUES
   ======================================== */

/* Override body styles for homepage */
body {
    padding-top: 0 !important;
    margin: 0 !important;
}

/* Modern Hero Section - Override existing styles */
.hero-modern {
    position: relative !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    overflow: hidden !important;
    padding: 80px 0 !important;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 25%, #f9fafb 50%, #eff6ff 75%, #dbeafe 100%) !important;
    margin: 0 !important;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    opacity: 0.3;
}

.hero-content-modern {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 48px !important;
    align-items: center !important;
    position: relative !important;
    z-index: 1 !important;
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding: 0 16px !important;
}

@media (min-width: 1024px) {
    .hero-content-modern {
        grid-template-columns: 1fr 1fr;
        gap: 64px;
        padding: 0 32px;
    }
}

.hero-text {
    text-align: center;
}

@media (min-width: 1024px) {
    .hero-text {
        text-align: left;
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #dbeafe;
    color: #1d4ed8;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
    animation: fadeInUp 0.6s ease-out;
}

.hero-title-modern {
    font-size: 36px !important;
    font-weight: 700 !important;
    line-height: 1.25 !important;
    color: #111827 !important;
    margin-bottom: 24px !important;
    animation: fadeInUp 0.6s ease-out 0.2s both !important;
    font-family: 'Poppins', sans-serif !important;
}

@media (min-width: 768px) {
    .hero-title-modern {
        font-size: 48px;
    }
}

@media (min-width: 1024px) {
    .hero-title-modern {
        font-size: 60px;
    }
}

.text-gradient {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description-modern {
    font-size: 18px;
    line-height: 1.625;
    color: #4b5563;
    margin-bottom: 32px;
    max-width: 600px;
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

@media (min-width: 1024px) {
    .hero-description-modern {
        font-size: 20px;
        max-width: none;
    }
}

.hero-stats {
    display: flex;
    gap: 32px;
    justify-content: center;
    margin-bottom: 32px;
    animation: fadeInUp 0.6s ease-out 0.6s both;
}

@media (min-width: 1024px) {
    .hero-stats {
        justify-content: flex-start;
    }
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #2563eb;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 14px;
    color: #6b7280;
    margin-top: 4px;
}

.hero-actions-modern {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 0.6s ease-out 0.8s both;
}

@media (min-width: 1024px) {
    .hero-actions-modern {
        justify-content: flex-start;
    }
}

/* Hero Visual Section */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-video-modern {
    position: relative;
    width: 100%;
    max-width: 600px;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    animation: fadeInUp 0.8s ease-out 1s both;
}

.hero-video-modern video {
    width: 100%;
    height: auto;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(37, 99, 235, 0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(37, 99, 235, 0.1) 100%);
    pointer-events: none;
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 18px;
    color: #2563eb;
}

.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 20%;
    left: -15%;
    animation-delay: 1s;
}

.card-3 {
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Button Styles for Hero */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
    border: 1px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.15s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    background-color: transparent;
    color: #2563eb;
    border-color: #2563eb;
}

.btn-outline:hover {
    background-color: #eff6ff;
    color: #1d4ed8;
    border-color: #1d4ed8;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-modern {
        min-height: 80vh;
        padding: 64px 0;
    }

    .hero-title-modern {
        font-size: 30px;
    }

    .hero-description-modern {
        font-size: 16px;
    }

    .hero-stats {
        gap: 16px;
    }

    .hero-actions-modern {
        flex-direction: column;
        align-items: stretch;
    }

    .floating-cards {
        display: none;
    }
}









/* ========================================
   HEADER CONSISTENCY OVERRIDES
   ======================================== */

/* Ensure header works properly on homepage */
@media (min-width: 769px) {
    body {
        padding-top: 70px !important;
    }

    .professional-header {
        position: fixed !important;
        top: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 50px !important;
    }

    .header-top-bar {
        display: none !important;
    }

    .main-navigation {
        display: none !important;
    }

    .search-section {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }
}

/* Animation for smooth loading */
.hero-video-container {
    animation: fadeInUp 0.8s ease-out;
}

/* Modern Services Section */
.services-modern {
    padding: 80px 0;
    background: #f9fafb;
}

.section-header {
    margin-bottom: 64px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #dbeafe;
    color: #1d4ed8;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
}

.section-title {
    font-size: 30px;
    font-weight: 700;
    line-height: 1.25;
    color: #111827;
    margin-bottom: 16px;
    font-family: 'Poppins', sans-serif;
}

@media (min-width: 768px) {
    .section-title {
        font-size: 36px;
    }
}

.section-description {
    font-size: 18px;
    color: #4b5563;
    max-width: 600px;
    margin: 0 auto;
}

.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
}

@media (min-width: 640px) {
    .container {
        padding: 0 24px;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 32px;
    }
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 64px;
}

.service-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #2563eb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    border-color: #bfdbfe;
}

.service-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.service-icon-primary {
    background: #dbeafe;
    color: #2563eb;
}

.service-icon-success {
    background: #dcfce7;
    color: #16a34a;
}

.service-icon-warning {
    background: #fef3c7;
    color: #d97706;
}

.service-icon-error {
    background: #fee2e2;
    color: #dc2626;
}

.service-icon-info {
    background: #dbeafe;
    color: #2563eb;
}

.service-title {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
}

.service-description {
    font-size: 16px;
    color: #4b5563;
    line-height: 1.625;
    margin-bottom: 16px;
}

.service-arrow {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #2563eb;
    font-size: 18px;
    transition: transform 0.15s ease;
}

.service-card:hover .service-arrow {
    transform: translateX(4px);
}

.services-cta {
    padding: 32px;
    background: white;
    border-radius: 16px;
    border: 2px dashed #d1d5db;
}

.cta-text {
    font-size: 18px;
    color: #4b5563;
    margin-bottom: 16px;
}

.text-center {
    text-align: center;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Modern Services Section -->
<section class="services-modern">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="ri-service-line"></i>
                <span>Nos Services</span>
            </div>
            <h2 class="section-title">
                Explorez nos <span class="text-gradient">catégories de services</span>
            </h2>
            <p class="section-description">
                Découvrez une large gamme de services professionnels proposés par nos freelances experts
            </p>
        </div>

        <div class="services-grid">
            <?php
            // Fetch categories from database
            $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
            $categories = $stmt->fetchAll();

            $icons = [
                'Développement' => 'ri-code-line',
                'Marketing Digital' => 'ri-megaphone-line',
                'Graphisme' => 'ri-palette-line',
                'Montage Vidéo' => 'ri-movie-line',
                'Analyse de Données' => 'ri-bar-chart-line',
                'Musique & Voix Off' => 'ri-music-line',
                'Rédaction de Scripts' => 'ri-quill-pen-line',
                'Traduction' => 'ri-translate-line',
                'Cours en Ligne' => 'ri-graduation-cap-line'
            ];

            $colors = [
                'Développement' => 'primary',
                'Marketing Digital' => 'success',
                'Graphisme' => 'warning',
                'Montage Vidéo' => 'error',
                'Analyse de Données' => 'info',
                'Musique & Voix Off' => 'primary',
                'Rédaction de Scripts' => 'success',
                'Traduction' => 'warning',
                'Cours en Ligne' => 'error'
            ];

            foreach ($categories as $category) {
                $icon = $icons[$category['name']] ?? 'ri-service-line';
                $color = $colors[$category['name']] ?? 'primary';

                echo '<a href="browse_services.php?category=' . urlencode($category['name']) . '" class="service-card">';
                echo '<div class="service-icon service-icon-' . $color . '">';
                echo '<i class="' . $icon . '"></i>';
                echo '</div>';
                echo '<h3 class="service-title">' . htmlspecialchars($category['name']) . '</h3>';
                echo '<p class="service-description">Découvrez nos experts en ' . strtolower(htmlspecialchars($category['name'])) . '</p>';
                echo '<div class="service-arrow">';
                echo '<i class="ri-arrow-right-line"></i>';
                echo '</div>';
                echo '</a>';
            }
            ?>
        </div>

        <div class="services-cta text-center">
            <p class="cta-text">Vous ne trouvez pas ce que vous cherchez ?</p>
            <a href="browse_services.php" class="btn btn-outline btn-lg">
                <i class="ri-search-line"></i>
                <span>Voir tous les services</span>
            </a>
        </div>
    </div>
</section>


<!-- Reviews Section -->
<section id="testimonials" class="testimonials">
    <h3>Avis de nos clients</h3>
    <div class="testimonial-cards">
        <?php
        // Get a few random reviews
        $stmt = $conn->query("
            SELECT r.rating, r.comment, u.full_name, u.user_type 
            FROM reviews r
            JOIN users u ON r.reviewer_id = u.id
            ORDER BY RAND()
            LIMIT 3
        ");
        
        $reviews = $stmt->fetchAll();
        
        if (count($reviews) > 0) {
            foreach ($reviews as $review) {
                echo '<div class="testimonial-card">';
                echo '<p>"' . htmlspecialchars_decode($review['comment']) . '"</p>';
                echo '<span>- ' . htmlspecialchars_decode($review['full_name']) . ', ' . 
                     ($review['user_type'] == 'client' ? 'client' : 'freelancer') . '</span>';
                echo '</div>';
            }
        } else {
            // Default testimonials if no reviews in database
            ?>
            <div class="testimonial-card">
                <p>"SawbLi a transformé ma carrière de freelance ! Les projets sont variés et les clients très
                    satisfaits."</p>
                <span>- Brahim, programmeur</span>
            </div>
            <div class="testimonial-card">
                <p>"Une plateforme simple et efficace pour trouver des freelances de qualité en marketing digital."</p>
                <span>- Lamyae, société de marketing</span>
            </div>
            <?php
        }
        ?>
    </div>
</section>

<!-- Stats Section -->
<section id="stats" class="stats">
    <h3>Statistiques SawbLi</h3>
    <div class="stat-boxes">
        <?php
        // Get actual stats from database
        $projectCount = $conn->query("SELECT COUNT(*) as count FROM projects")->fetch()['count'] ?? 0;
        $freelancerCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'freelancer'")->fetch()['count'] ?? 0;
        $clientCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")->fetch()['count'] ?? 0;
        
        // If stats are too low, use default values
        $projectCount = max($projectCount, 1200);
        $freelancerCount = max($freelancerCount, 300);
        $clientCount = max($clientCount, 500);
        ?>
        <div class="stat-box">
            <h4><?= $projectCount ?>+</h4>
            <p>Projets réalisés</p>
        </div>
        <div class="stat-box">
            <h4><?= $freelancerCount ?>+</h4>
            <p>Indépendants inscrits</p>
        </div>
        <div class="stat-box">
            <h4><?= $clientCount ?>+</h4>
            <p>Clients satisfaits</p>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="cta" class="cta">
    <h3>Êtes-vous prêt à devenir freelance ou à embaucher les meilleurs ?</h3>
    <p>Rejoignez SawbLi et commencez votre aventure maintenant !</p>
    <a href="signup.php" class="cta-btn">Rejoignez-nous</a>
</section>

<!-- Why Choose Us Section -->
<section id="why-us" class="why-us">
    <h3>Pourquoi choisir SawbLi ?</h3>
    <div class="reasons">
        <div class="reason-box">
            <h4>Facilité d'utilisation</h4>
            <p>Notre interface est simple et conviviale.</p>
        </div>
        <div class="reason-box">
            <h4>Assistance continue</h4>
            <p>Assistance technique 24h/24 et 7j/7 disponible pour tous les membres.</p>
        </div>
        <div class="reason-box">
            <h4>Haute fiabilité</h4>
            <p>Systèmes de sécurité pour assurer des transactions sécurisées.</p>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
