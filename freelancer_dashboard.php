<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Ensure the user is logged in and is a freelancer
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'freelancer'");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    $_SESSION['error'] = "Vous n'avez pas l'autorisation d'accéder à cette page.";
    header("Location: index.php");
    exit;
}

// Get freelancer's services
$stmt = $conn->prepare("
    SELECT s.*, COUNT(p.id) as project_count 
    FROM services s
    LEFT JOIN projects p ON s.id = p.service_id
    WHERE s.user_id = ?
    GROUP BY s.id
    ORDER BY s.created_at DESC
");
$stmt->execute([$user_id]);
$services = $stmt->fetchAll();

// Get active projects
$stmt = $conn->prepare("
    SELECT p.*, u.username as client_name, s.title as service_title
    FROM projects p
    JOIN users u ON p.client_id = u.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.freelancer_id = ? AND p.status != 'completed' AND p.status != 'cancelled'
    ORDER BY p.created_at DESC
");
$stmt->execute([$user_id]);
$active_projects = $stmt->fetchAll();

// Get completed projects
$stmt = $conn->prepare("
    SELECT p.*, u.username as client_name, s.title as service_title
    FROM projects p
    JOIN users u ON p.client_id = u.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.freelancer_id = ? AND p.status = 'completed'
    ORDER BY p.completion_date DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$completed_projects = $stmt->fetchAll();

// Get reviews received
$stmt = $conn->prepare("
    SELECT r.*, p.title as project_title, u.username as reviewer_name
    FROM reviews r
    JOIN projects p ON r.project_id = p.id
    JOIN users u ON r.reviewer_id = u.id
    WHERE r.reviewee_id = ?
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$reviews = $stmt->fetchAll();

// Calculate statistics
$total_earnings = $conn->prepare("
    SELECT SUM(price) as total
    FROM projects
    WHERE freelancer_id = ? AND status = 'completed'
");
$total_earnings->execute([$user_id]);
$earnings = $total_earnings->fetch()['total'] ?? 0;

$total_projects = $conn->prepare("
    SELECT COUNT(*) as count
    FROM projects
    WHERE freelancer_id = ?
");
$total_projects->execute([$user_id]);
$projects_count = $total_projects->fetch()['count'] ?? 0;

$avg_rating = $conn->prepare("
    SELECT AVG(rating) as average
    FROM reviews
    WHERE reviewee_id = ?
");
$avg_rating->execute([$user_id]);
$rating = $avg_rating->fetch()['average'] ?? 0;

// Check subscription status
$subscription = $conn->prepare("
    SELECT * FROM subscriptions 
    WHERE user_id = ? AND is_active = TRUE 
    ORDER BY plan_type DESC 
    LIMIT 1
");
$subscription->execute([$user_id]);
$subscription_data = $subscription->fetch();

$is_pro = ($subscription_data && in_array($subscription_data['plan_type'], ['pro', 'premium']));

include 'includes/header.php';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dashboard Freelancer - SawbLi</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 text-gray-800">

  <!-- Dashboard Content -->
  <div class="dashboard-container">
    <!-- Welcome Header -->
    <div class="dashboard-header">
      <h2>Bienvenue sur votre tableau de bord, <?= htmlspecialchars($user['full_name']) ?> !</h2>
      <p>Gérez vos services, communiquez avec les clients, et développez votre activité.</p>
      
      <?php if (!$is_pro): ?>
      <div class="bg-yellow-100 mt-4 p-4 rounded-lg">
        <p class="font-semibold">Vous utilisez un compte gratuit. <a href="#abonnement" class="text-blue-600 underline">Passez à SawbLi Pro</a> pour débloquer toutes les fonctionnalités !</p>
      </div>
      <?php endif; ?>
    </div>
    
    <!-- Stats Section -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <h3><?= number_format($projects_count) ?></h3>
        <p>Projets totaux</p>
      </div>
      <div class="stat-card">
        <h3><?= number_format($earnings, 2) ?> DH</h3>
        <p>Revenus totaux</p>
      </div>
      <div class="stat-card">
        <h3><?= number_format($rating, 1) ?>/5</h3>
        <p>Note moyenne</p>
      </div>
      <div class="stat-card">
        <h3><?= count($services) ?></h3>
        <p>Services actifs</p>
      </div>
    </div>
    
    <!-- Services Section -->
    <h3 class="text-2xl font-bold mb-6">Vos Services</h3>
    
    <?php if (count($services) > 0): ?>
    <div class="services-list">
      <?php foreach ($services as $service): ?>
      <div class="service-card">
        <div class="service-card-header">
          <h3><?= htmlspecialchars($service['title']) ?></h3>
          <p class="text-sm text-gray-500">Catégorie: <?= htmlspecialchars($service['category']) ?></p>
        </div>
        <div class="service-card-body">
          <p><?= nl2br(htmlspecialchars(substr($service['description'], 0, 100) . (strlen($service['description']) > 100 ? '...' : ''))) ?></p>
          <p class="mt-2 text-sm"><?= $service['project_count'] ?> projet(s) réalisé(s)</p>
        </div>
        <div class="service-card-footer">
          <span class="service-price"><?= number_format($service['price'], 2) ?> DH</span>
          <div class="service-actions">
            <a href="edit_service.php?id=<?= $service['id'] ?>" class="btn btn-secondary">Modifier</a>
            <a href="delete_service.php?id=<?= $service['id'] ?>" class="btn btn-danger delete-service-btn">Supprimer</a>
          </div>
        </div>
      </div>
      <?php endforeach; ?>
    </div>
    
    <div class="mt-6 text-center">
      <a href="add_service.php" class="btn btn-primary">+ Ajouter un nouveau service</a>
    </div>
    <?php else: ?>
    <div class="bg-white p-6 rounded-lg shadow text-center">
      <p class="mb-4">Vous n'avez pas encore créé de services.</p>
      <a href="add_service.php" class="btn btn-primary">Ajouter votre premier service</a>
    </div>
    <?php endif; ?>
    
    <!-- Projects Section -->
    <div class="mt-10 dashboard-content">
      <!-- Active Projects -->
      <div class="dashboard-card">
        <h3>Projets en cours</h3>
        <div class="dashboard-card-content">
          <?php if (count($active_projects) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($active_projects as $project): ?>
              <li class="py-3">
                <p class="font-semibold"><?= htmlspecialchars($project['title']) ?></p>
                <p class="text-sm">Client: <?= htmlspecialchars($project['client_name']) ?></p>
                <p class="text-sm">Prix: <?= number_format($project['price'], 2) ?> DH</p>
                <p class="text-sm">Statut: 
                  <span class="px-2 py-1 rounded text-xs
                    <?= $project['status'] === 'in_progress' ? 'bg-blue-100 text-blue-700' : 'bg-yellow-100 text-yellow-700' ?>">
                    <?= $project['status'] === 'in_progress' ? 'En cours' : 'En attente' ?>
                  </span>
                </p>
                <a href="view_project.php?id=<?= $project['id'] ?>" class="text-blue-600 text-sm mt-2 inline-block">Voir détails →</a>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Aucun projet en cours.</p>
          <?php endif; ?>
        </div>
      </div>
      
      <!-- Completed Projects -->
      <div class="dashboard-card">
        <h3>Projets terminés</h3>
        <div class="dashboard-card-content">
          <?php if (count($completed_projects) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($completed_projects as $project): ?>
              <li class="py-3">
                <p class="font-semibold"><?= htmlspecialchars($project['title']) ?></p>
                <p class="text-sm">Client: <?= htmlspecialchars($project['client_name']) ?></p>
                <p class="text-sm">Prix: <?= number_format($project['price'], 2) ?> DH</p>
                <p class="text-sm">Terminé le: <?= date('d/m/Y', strtotime($project['completion_date'])) ?></p>
                <a href="view_project.php?id=<?= $project['id'] ?>" class="text-blue-600 text-sm mt-2 inline-block">Voir détails →</a>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Aucun projet terminé.</p>
          <?php endif; ?>
        </div>
      </div>
      
      <!-- Reviews -->
      <div class="dashboard-card">
        <h3>Vos avis</h3>
        <div class="dashboard-card-content">
          <?php if (count($reviews) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($reviews as $review): ?>
              <li class="py-3">
                <div class="flex justify-between">
                  <p class="font-semibold"><?= htmlspecialchars($review['project_title']) ?></p>
                  <p class="text-yellow-500">
                    <?php 
                    for ($i = 1; $i <= 5; $i++) {
                      echo $i <= $review['rating'] ? '★' : '☆';
                    }
                    ?>
                  </p>
                </div>
                <p class="text-sm italic">"<?= htmlspecialchars($review['comment']) ?>"</p>
                <p class="text-sm text-right">- <?= htmlspecialchars($review['reviewer_name']) ?></p>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Aucun avis reçu pour le moment.</p>
          <?php endif; ?>
        </div>
      </div>
    </div>
    
    <!-- Subscription Section -->
    <section id="abonnement" class="mt-16">
      <h3 class="text-2xl font-bold mb-6 text-center">Passez à SawbLi Pro</h3>
      <p class="text-center max-w-3xl mx-auto mb-10">Augmentez votre visibilité, obtenez plus de projets et débloquez des fonctionnalités exclusives.</p>

      <div class="grid md:grid-cols-3 gap-6">
        <!-- Plan Gratuit -->
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-lg font-bold mb-4">Gratuit</h4>
          <p class="text-3xl font-bold mb-4">0 DH</p>
          <ul class="space-y-2 mb-6">
            <li>3 services actifs</li>
            <li>Support de base</li>
          </ul>
          <button class="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed w-full">
            <?= (!$is_pro) ? 'Plan Actuel' : 'Plan de Base' ?>
          </button>
        </div>

        <!-- Plan Pro -->
        <div class="bg-yellow-100 p-6 rounded-lg shadow border-2 border-yellow-400">
          <h4 class="text-lg font-bold mb-4">Pro</h4>
          <p class="text-3xl font-bold mb-4">99 DH / mois</p>
          <ul class="space-y-2 mb-6">
            <li>Services illimités</li>
            <li>Visibilité accrue</li>
            <li>Badge Pro</li>
            <li>Support prioritaire</li>
          </ul>
          <?php if ($is_pro && $subscription_data['plan_type'] === 'pro'): ?>
            <button class="bg-green-500 text-white px-4 py-2 rounded cursor-not-allowed w-full">Abonnement Actif</button>
          <?php else: ?>
            <a href="subscribe.php?plan=pro" class="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-semibold px-4 py-2 rounded transition block text-center">S'abonner</a>
          <?php endif; ?>
        </div>

        <!-- Plan Premium -->
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-lg font-bold mb-4">Premium</h4>
          <p class="text-3xl font-bold mb-4">199 DH / mois</p>
          <ul class="space-y-2 mb-6">
            <li>Tous les avantages Pro</li>
            <li>Mise en avant sur la page d'accueil</li>
            <li>Analyse de profil</li>
          </ul>
          <?php if ($is_pro && $subscription_data['plan_type'] === 'premium'): ?>
            <button class="bg-green-500 text-white px-4 py-2 rounded cursor-not-allowed w-full">Abonnement Actif</button>
          <?php else: ?>
            <a href="subscribe.php?plan=premium" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded transition block text-center">S'abonner</a>
          <?php endif; ?>
        </div>
      </div>
    </section>
  </div>

<?php include 'includes/footer.php'; ?>
