<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) && !isset($_GET['id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

// Get profile ID (either the logged-in user or the profile being viewed)
$profile_id = isset($_GET['id']) ? intval($_GET['id']) : $_SESSION['user_id'];

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$profile_id]);
$user = $stmt->fetch();

if (!$user) {
    $_SESSION['error'] = "Utilisateur introuvable.";
    header("Location: index.php");
    exit;
}

// Check if this is the logged-in user's profile
$is_own_profile = isset($_SESSION['user_id']) && $_SESSION['user_id'] === $profile_id;

// Handle portfolio upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_portfolio']) && $is_own_profile) {
    $title = trim($_POST['portfolio_title'] ?? '');
    $description = trim($_POST['portfolio_description'] ?? '');
    $project_url = trim($_POST['project_url'] ?? '');
    $technologies = trim($_POST['technologies'] ?? '');

    if (!empty($title) && !empty($description)) {
        // Handle file upload
        $image_path = null;
        if (isset($_FILES['portfolio_image']) && $_FILES['portfolio_image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/portfolio/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['portfolio_image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            if (in_array($file_extension, $allowed_extensions)) {
                $filename = uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;

                if (move_uploaded_file($_FILES['portfolio_image']['tmp_name'], $upload_path)) {
                    $image_path = $filename;
                }
            }
        }

        // Insert portfolio item
        $stmt = $conn->prepare("
            INSERT INTO portfolio (user_id, title, description, image_path, project_url, technologies, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$profile_id, $title, $description, $image_path, $project_url, $technologies]);

        $_SESSION['success'] = "Projet ajouté au portfolio avec succès!";
        header("Location: profile.php");
        exit;
    }
}

// Get subscription status
$stmt = $conn->prepare("
    SELECT s.plan_type 
    FROM subscriptions s 
    WHERE s.user_id = ? AND s.is_active = TRUE 
    AND (s.end_date IS NULL OR s.end_date > CURRENT_TIMESTAMP)
    ORDER BY s.plan_type DESC
    LIMIT 1
");
$stmt->execute([$profile_id]);
$subscription = $stmt->fetch();
$is_pro = ($subscription && in_array($subscription['plan_type'], ['pro', 'premium']));

// If this is a freelancer, get their services
$services = [];
if ($user['user_type'] === 'freelancer') {
    $stmt = $conn->prepare("
        SELECT s.*, COUNT(p.id) as project_count 
        FROM services s
        LEFT JOIN projects p ON s.id = p.service_id
        WHERE s.user_id = ? AND s.status = 'active'
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ");
    $stmt->execute([$profile_id]);
    $services = $stmt->fetchAll();
}

// Get projects count
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM projects
    WHERE " . ($user['user_type'] === 'freelancer' ? "freelancer_id" : "client_id") . " = ? AND status = 'completed'
");
$stmt->execute([$profile_id]);
$completed_projects = $stmt->fetch()['count'];

// Get average rating
$stmt = $conn->prepare("
    SELECT AVG(rating) as average, COUNT(*) as count
    FROM reviews
    WHERE reviewee_id = ?
");
$stmt->execute([$profile_id]);
$rating_data = $stmt->fetch();
$avg_rating = $rating_data['average'] ? round($rating_data['average'], 1) : 0;
$rating_count = $rating_data['count'];

// Get recent reviews
$stmt = $conn->prepare("
    SELECT r.*, p.title as project_title, u.username as reviewer_name
    FROM reviews r
    JOIN projects p ON r.project_id = p.id
    JOIN users u ON r.reviewer_id = u.id
    WHERE r.reviewee_id = ?
    ORDER BY r.created_at DESC
    LIMIT 3
");
$stmt->execute([$profile_id]);
$reviews = $stmt->fetchAll();

// Get portfolio items
$portfolio_items = [];
if ($user['user_type'] === 'freelancer') {
    // Create portfolio table if it doesn't exist
    $conn->exec("
        CREATE TABLE IF NOT EXISTS portfolio (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            image_path VARCHAR(255),
            project_url VARCHAR(255),
            technologies TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");

    $stmt = $conn->prepare("
        SELECT * FROM portfolio
        WHERE user_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$profile_id]);
    $portfolio_items = $stmt->fetchAll();
}

// Get skills (if available)
$skills = [];
if (!empty($user['skills'])) {
    $skills = explode(',', $user['skills']);
    $skills = array_map('trim', $skills);
}

include 'includes/header.php';
?>

<!-- Professional Profile Layout -->
<div class="professional-profile">
    <!-- Hero Section -->
    <div class="profile-hero">
        <div class="hero-background"></div>
        <div class="hero-content">
            <div class="profile-avatar">
                <?php if (!empty($user['profile_picture'])): ?>
                    <img src="uploads/profile_pictures/<?= htmlspecialchars($user['profile_picture']) ?>"
                         alt="<?= htmlspecialchars($user['username']) ?>"
                         class="avatar-image">
                <?php else: ?>
                    <div class="avatar-placeholder">
                        <i class="ri-user-line"></i>
                    </div>
                <?php endif; ?>
                <?php if ($is_pro): ?>
                    <div class="pro-badge">
                        <i class="ri-vip-crown-line"></i>
                        <span>PRO</span>
                    </div>
                <?php endif; ?>
            </div>

            <div class="profile-header-info">
                <h1 class="profile-name"><?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></h1>
                <p class="profile-username">@<?= htmlspecialchars($user['username']) ?></p>
                <p class="profile-role">
                    <i class="ri-briefcase-line"></i>
                    <?= $user['user_type'] === 'freelancer' ? 'Freelancer Professionnel' : 'Client' ?>
                </p>

                <?php if ($user['user_type'] === 'freelancer' && $avg_rating > 0): ?>
                    <div class="rating-display">
                        <div class="stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="ri-star-<?= $i <= floor($avg_rating) ? 'fill' : ($i - 0.5 <= $avg_rating ? 'half-fill' : 'line') ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="rating-text"><?= $avg_rating ?>/5 (<?= $rating_count ?> avis)</span>
                    </div>
                <?php endif; ?>

                <div class="profile-actions">
                    <?php if ($is_own_profile): ?>
                        <a href="edit_profile.php" class="btn btn-primary">
                            <i class="ri-edit-line"></i>
                            <span>Modifier Profil</span>
                        </a>
                        <a href="<?= $user['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php' ?>" class="btn btn-secondary">
                            <i class="ri-dashboard-line"></i>
                            <span>Dashboard</span>
                        </a>
                    <?php elseif ($user['user_type'] === 'freelancer'): ?>
                        <a href="contact.php?id=<?= $profile_id ?>" class="btn btn-primary">
                            <i class="ri-message-line"></i>
                            <span>Contacter</span>
                        </a>
                        <a href="browse_services.php?freelancer=<?= $profile_id ?>" class="btn btn-secondary">
                            <i class="ri-service-line"></i>
                            <span>Voir Services</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="profile-main">
        <div class="profile-sidebar">
            <!-- Contact Info Card -->
            <div class="info-card">
                <h3 class="card-title">
                    <i class="ri-contacts-line"></i>
                    Informations de contact
                </h3>
                <div class="contact-info">
                    <?php if (!empty($user['email'])): ?>
                        <div class="contact-item">
                            <i class="ri-mail-line"></i>
                            <span><?= htmlspecialchars($user['email']) ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($user['phone'])): ?>
                        <div class="contact-item">
                            <i class="ri-phone-line"></i>
                            <span><?= htmlspecialchars($user['phone']) ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($user['location'])): ?>
                        <div class="contact-item">
                            <i class="ri-map-pin-line"></i>
                            <span><?= htmlspecialchars($user['location']) ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="contact-item">
                        <i class="ri-calendar-line"></i>
                        <span>Membre depuis <?= date('M Y', strtotime($user['created_at'])) ?></span>
                    </div>
                </div>
            </div>

            <!-- Stats Card -->
            <div class="info-card">
                <h3 class="card-title">
                    <i class="ri-bar-chart-line"></i>
                    Statistiques
                </h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number"><?= number_format($completed_projects) ?></div>
                        <div class="stat-label">Projets complétés</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $user['user_type'] === 'freelancer' ? count($services) : '-' ?></div>
                        <div class="stat-label"><?= $user['user_type'] === 'freelancer' ? 'Services actifs' : 'Projets commandés' ?></div>
                    </div>
                    <?php if ($user['user_type'] === 'freelancer'): ?>
                        <div class="stat-item">
                            <div class="stat-number"><?= count($portfolio_items) ?></div>
                            <div class="stat-label">Projets portfolio</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Skills Card (for freelancers) -->
            <?php if ($user['user_type'] === 'freelancer' && !empty($skills)): ?>
                <div class="info-card">
                    <h3 class="card-title">
                        <i class="ri-tools-line"></i>
                        Compétences
                    </h3>
                    <div class="skills-list">
                        <?php foreach ($skills as $skill): ?>
                            <span class="skill-tag"><?= htmlspecialchars($skill) ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="profile-content">
            <!-- About Section -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="ri-user-heart-line"></i>
                        À propos
                    </h2>
                </div>
                <div class="about-content">
                    <?php if (!empty($user['bio'])): ?>
                        <p><?= nl2br(htmlspecialchars($user['bio'])) ?></p>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="ri-file-text-line"></i>
                            <p>Aucune biographie disponible.</p>
                            <?php if ($is_own_profile): ?>
                                <a href="edit_profile.php" class="btn btn-outline">Ajouter une biographie</a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Portfolio Section (for freelancers) -->
            <?php if ($user['user_type'] === 'freelancer'): ?>
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="ri-folder-open-line"></i>
                            Portfolio
                        </h2>
                        <?php if ($is_own_profile): ?>
                            <button class="btn btn-primary btn-sm" onclick="openPortfolioModal()">
                                <i class="ri-add-line"></i>
                                <span>Ajouter un projet</span>
                            </button>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($portfolio_items)): ?>
                        <div class="portfolio-grid">
                            <?php foreach ($portfolio_items as $item): ?>
                                <div class="portfolio-item">
                                    <?php if ($item['image_path']): ?>
                                        <div class="portfolio-image">
                                            <img src="uploads/portfolio/<?= htmlspecialchars($item['image_path']) ?>"
                                                 alt="<?= htmlspecialchars($item['title']) ?>">
                                            <div class="portfolio-overlay">
                                                <div class="portfolio-actions">
                                                    <?php if ($item['project_url']): ?>
                                                        <a href="<?= htmlspecialchars($item['project_url']) ?>"
                                                           target="_blank" class="portfolio-btn">
                                                            <i class="ri-external-link-line"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <button class="portfolio-btn" onclick="viewPortfolioItem(<?= $item['id'] ?>)">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="portfolio-content">
                                        <h3 class="portfolio-title"><?= htmlspecialchars($item['title']) ?></h3>
                                        <p class="portfolio-description"><?= htmlspecialchars(substr($item['description'], 0, 100)) ?><?= strlen($item['description']) > 100 ? '...' : '' ?></p>

                                        <?php if ($item['technologies']): ?>
                                            <div class="portfolio-tech">
                                                <?php
                                                $techs = explode(',', $item['technologies']);
                                                foreach ($techs as $tech):
                                                ?>
                                                    <span class="tech-tag"><?= htmlspecialchars(trim($tech)) ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="portfolio-meta">
                                            <span class="portfolio-date">
                                                <i class="ri-calendar-line"></i>
                                                <?= date('M Y', strtotime($item['created_at'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="ri-folder-open-line"></i>
                            <h3>Aucun projet dans le portfolio</h3>
                            <p>Ajoutez vos meilleurs projets pour impressionner vos clients potentiels.</p>
                            <?php if ($is_own_profile): ?>
                                <button class="btn btn-primary" onclick="openPortfolioModal()">
                                    <i class="ri-add-line"></i>
                                    <span>Ajouter votre premier projet</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <!-- Services Section (for freelancers) -->
            <?php if ($user['user_type'] === 'freelancer'): ?>
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="ri-service-line"></i>
                            Services proposés
                        </h2>
                        <?php if ($is_own_profile): ?>
                            <a href="add_service.php" class="btn btn-primary btn-sm">
                                <i class="ri-add-line"></i>
                                <span>Ajouter un service</span>
                            </a>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($services)): ?>
                        <div class="services-grid">
                            <?php foreach ($services as $service): ?>
                                <div class="service-card">
                                    <div class="service-header">
                                        <div class="service-category"><?= htmlspecialchars($service['category']) ?></div>
                                        <div class="service-price"><?= number_format($service['price'], 2) ?> DH</div>
                                    </div>

                                    <div class="service-content">
                                        <h3 class="service-title"><?= htmlspecialchars($service['title']) ?></h3>
                                        <p class="service-description">
                                            <?= htmlspecialchars(substr($service['description'], 0, 120)) ?>
                                            <?= strlen($service['description']) > 120 ? '...' : '' ?>
                                        </p>
                                    </div>

                                    <div class="service-footer">
                                        <div class="service-stats">
                                            <span class="stat">
                                                <i class="ri-shopping-bag-line"></i>
                                                <?= $service['project_count'] ?> commandes
                                            </span>
                                        </div>
                                        <a href="dd_service.php?id=<?= $service['id'] ?>" class="btn btn-outline btn-sm">
                                            Voir détails
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="ri-service-line"></i>
                            <h3>Aucun service proposé</h3>
                            <p>Commencez à proposer vos services pour attirer des clients.</p>
                            <?php if ($is_own_profile): ?>
                                <a href="add_service.php" class="btn btn-primary">
                                    <i class="ri-add-line"></i>
                                    <span>Créer votre premier service</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <!-- Reviews Section -->
            <?php if ($user['user_type'] === 'freelancer'): ?>
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="ri-star-line"></i>
                            Avis clients
                        </h2>
                        <?php if ($rating_count > 3): ?>
                            <a href="reviews.php?user=<?= $profile_id ?>" class="btn btn-outline btn-sm">
                                Voir tous les avis (<?= $rating_count ?>)
                            </a>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($reviews)): ?>
                        <div class="reviews-list">
                            <?php foreach ($reviews as $review): ?>
                                <div class="review-card">
                                    <div class="review-header">
                                        <div class="reviewer-info">
                                            <div class="reviewer-avatar">
                                                <?= strtoupper(substr($review['reviewer_name'], 0, 1)) ?>
                                            </div>
                                            <div class="reviewer-details">
                                                <h4 class="reviewer-name"><?= htmlspecialchars($review['reviewer_name']) ?></h4>
                                                <p class="review-project"><?= htmlspecialchars($review['project_title']) ?></p>
                                            </div>
                                        </div>
                                        <div class="review-rating">
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="ri-star-<?= $i <= $review['rating'] ? 'fill' : 'line' ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="review-date"><?= date('d/m/Y', strtotime($review['created_at'])) ?></span>
                                        </div>
                                    </div>
                                    <div class="review-content">
                                        <p>"<?= htmlspecialchars($review['comment']) ?>"</p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="ri-star-line"></i>
                            <h3>Aucun avis pour le moment</h3>
                            <p>Les avis de vos clients apparaîtront ici après la réalisation de projets.</p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Portfolio Modal -->
<?php if ($is_own_profile && $user['user_type'] === 'freelancer'): ?>
    <div id="portfolioModal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="ri-add-line"></i>
                    Ajouter un projet au portfolio
                </h3>
                <button class="modal-close" onclick="closePortfolioModal()">
                    <i class="ri-close-line"></i>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="modal-form">
                <input type="hidden" name="add_portfolio" value="1">

                <div class="form-group">
                    <label for="portfolio_title">Titre du projet *</label>
                    <input type="text" id="portfolio_title" name="portfolio_title" required
                           placeholder="Ex: Site web e-commerce pour boutique de mode">
                </div>

                <div class="form-group">
                    <label for="portfolio_description">Description *</label>
                    <textarea id="portfolio_description" name="portfolio_description" required
                              placeholder="Décrivez votre projet, les défis relevés et les solutions apportées..."></textarea>
                </div>

                <div class="form-group">
                    <label for="portfolio_image">Image du projet</label>
                    <input type="file" id="portfolio_image" name="portfolio_image"
                           accept="image/*" onchange="previewImage(this)">
                    <div id="imagePreview" class="image-preview" style="display: none;">
                        <img id="previewImg" src="" alt="Aperçu">
                    </div>
                </div>

                <div class="form-group">
                    <label for="project_url">URL du projet (optionnel)</label>
                    <input type="url" id="project_url" name="project_url"
                           placeholder="https://exemple.com">
                </div>

                <div class="form-group">
                    <label for="technologies">Technologies utilisées</label>
                    <input type="text" id="technologies" name="technologies"
                           placeholder="Ex: HTML, CSS, JavaScript, PHP, MySQL">
                    <small class="form-help">Séparez les technologies par des virgules</small>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closePortfolioModal()">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="ri-add-line"></i>
                        Ajouter au portfolio
                    </button>
                </div>
            </form>
        </div>
    </div>
<?php endif; ?>

<style>
/* ========================================
   PROFESSIONAL PROFILE STYLES
   ======================================== */

.professional-profile {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

/* Hero Section */
.profile-hero {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    padding: 3rem 0 2rem;
    margin-bottom: 2rem;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-placeholder i {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.8);
}

.pro-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a202c;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.profile-header-info {
    flex: 1;
}

.profile-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.profile-username {
    font-size: 1.1rem;
    opacity: 0.8;
    margin: 0 0 0.5rem 0;
}

.profile-role {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.1rem;
    color: #ffd700;
}

.rating-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.profile-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Main Content Layout */
.profile-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Sidebar */
.profile-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.contact-item i {
    color: var(--primary-color);
    width: 16px;
    flex-shrink: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9ff;
    border-radius: 8px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Content Area */
.profile-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.content-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.about-content p {
    line-height: 1.6;
    color: var(--text-color);
    margin: 0;
}

/* Portfolio Grid */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.portfolio-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.portfolio-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-actions {
    display: flex;
    gap: 0.5rem;
}

.portfolio-btn {
    background: white;
    color: var(--primary-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.portfolio-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 0.5rem 0;
}

.portfolio-description {
    color: var(--text-color);
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.portfolio-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.tech-tag {
    background: #e2e8f0;
    color: var(--text-color);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.portfolio-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.portfolio-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-category {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.service-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.service-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 0.5rem 0;
}

.service-description {
    color: var(--text-color);
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.service-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.service-stats {
    display: flex;
    gap: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* Reviews */
.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.review-card {
    background: #f8f9ff;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.reviewer-name {
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 0.25rem 0;
}

.review-project {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 0;
}

.review-rating {
    text-align: right;
}

.review-rating .stars {
    color: #ffd700;
    margin-bottom: 0.25rem;
}

.review-date {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.review-content p {
    font-style: italic;
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 3rem;
    color: var(--primary-color);
    opacity: 0.5;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(19, 64, 116, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
}

.modal-container {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f1f5f9;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f1f5f9;
    color: var(--primary-color);
}

.modal-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(19, 64, 116, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-help {
    display: block;
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

.image-preview {
    margin-top: 1rem;
    text-align: center;
}

.image-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    border-top: 1px solid #f1f5f9;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .profile-main {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .profile-sidebar {
        order: 2;
    }

    .profile-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .profile-name {
        font-size: 2rem;
    }

    .profile-main {
        padding: 0 1rem;
    }

    .portfolio-grid,
    .services-grid {
        grid-template-columns: 1fr;
    }

    .profile-actions {
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .modal-overlay {
        padding: 1rem;
    }

    .modal-form {
        padding: 1.5rem;
    }

    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .profile-hero {
        padding: 2rem 0 1.5rem;
    }

    .avatar-image,
    .avatar-placeholder {
        width: 80px;
        height: 80px;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .content-section {
        padding: 1.5rem;
    }

    .info-card {
        padding: 1rem;
    }
}
</style>

<script>
// Portfolio Modal Functions
function openPortfolioModal() {
    document.getElementById('portfolioModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closePortfolioModal() {
    document.getElementById('portfolioModal').style.display = 'none';
    document.body.style.overflow = 'auto';

    // Reset form
    const form = document.querySelector('.modal-form');
    if (form) {
        form.reset();
        const preview = document.getElementById('imagePreview');
        if (preview) {
            preview.style.display = 'none';
        }
    }
}

// Image Preview Function
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');

            img.src = e.target.result;
            preview.style.display = 'block';
        };

        reader.readAsDataURL(input.files[0]);
    }
}

// Portfolio Item Viewer (placeholder for future implementation)
function viewPortfolioItem(itemId) {
    // This could open a detailed view modal
    console.log('Viewing portfolio item:', itemId);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('portfolioModal');
    if (modal && e.target === modal) {
        closePortfolioModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePortfolioModal();
    }
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading states to buttons
document.querySelectorAll('.btn').forEach(btn => {
    btn.addEventListener('click', function() {
        if (this.type === 'submit') {
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            setTimeout(() => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
            }, 2000);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
