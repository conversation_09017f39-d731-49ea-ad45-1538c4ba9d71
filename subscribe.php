<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Get user information
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    $_SESSION['error'] = "Utilisateur introuvable.";
    header("Location: dashboard.php");
    exit;
}

// Check if plan parameter is provided
if (!isset($_GET['plan']) || !in_array($_GET['plan'], ['pro', 'premium'])) {
    $_SESSION['error'] = "Plan d'abonnement invalide.";
    header("Location: " . ($user['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php'));
    exit;
}

$plan = $_GET['plan'];
$plan_name = $plan === 'pro' ? 'Pro' : 'Premium';
$plan_price = $plan === 'pro' ? 99 : 199;

// Check if user already has an active subscription
$stmt = $conn->prepare("
    SELECT * FROM subscriptions 
    WHERE user_id = ? AND is_active = TRUE 
    AND (end_date IS NULL OR end_date > CURRENT_TIMESTAMP)
    ORDER BY plan_type DESC
    LIMIT 1
");
$stmt->execute([$user_id]);
$current_subscription = $stmt->fetch();

// Process subscription payment
$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['confirm_subscription'])) {
    $card_number = sanitize($_POST['card_number'] ?? '');
    $card_name = sanitize($_POST['card_name'] ?? '');
    $expiry_date = sanitize($_POST['expiry_date'] ?? '');
    $cvv = sanitize($_POST['cvv'] ?? '');
    
    // Basic validation
    if (empty($card_number) || empty($card_name) || empty($expiry_date) || empty($cvv)) {
        $error = "Tous les champs sont requis.";
    } elseif (strlen($card_number) < 16 || !is_numeric($card_number)) {
        $error = "Numéro de carte invalide.";
    } elseif (strlen($cvv) < 3 || !is_numeric($cvv)) {
        $error = "CVV invalide.";
    } else {
        try {
            // In a real application, you would process payment through a payment gateway here
            
            // Set end date for any existing active subscriptions
            $stmt = $conn->prepare("
                UPDATE subscriptions 
                SET is_active = FALSE, end_date = CURRENT_TIMESTAMP
                WHERE user_id = ? AND is_active = TRUE
            ");
            $stmt->execute([$user_id]);
            
            // Create new subscription with 30 days validity
            $stmt = $conn->prepare("
                INSERT INTO subscriptions (user_id, plan_type, start_date, end_date, is_active) 
                VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', TRUE)
            ");
            $stmt->execute([$user_id, $plan]);
            
            // Update user pro status
            $stmt = $conn->prepare("UPDATE users SET is_pro = TRUE WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $_SESSION['success'] = "Félicitations ! Votre abonnement SawbLi $plan_name a été activé avec succès.";
            header("Location: " . ($user['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php'));
            exit;
        } catch (PDOException $e) {
            $error = "Erreur lors de l'activation de l'abonnement: " . $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2>S'abonner à SawbLi <?= $plan_name ?></h2>
    
    <?php if (!empty($error)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p><?= $error ?></p>
        </div>
    <?php endif; ?>
    
<?php if ($current_subscription): ?>
    <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">
        <p>
            Vous avez déjà un abonnement <?= ucfirst($current_subscription['plan_type']) ?> actif 
            jusqu'au 
            <?php 
                if (!empty($current_subscription['end_date'])) {
                    echo date('d/m/Y', strtotime($current_subscription['end_date']));
                } else {
                    echo "une date inconnue";
                }
            ?>.
            En souscrivant à un nouvel abonnement, votre abonnement actuel sera remplacé.
        </p>
    </div>
<?php endif; ?>

    
    <div class="bg-blue-50 p-6 rounded-lg mb-6">
        <h3 class="text-xl font-bold mb-4">Détails de l'abonnement</h3>
        <ul class="space-y-2">
            <?php if ($plan === 'pro'): ?>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Services illimités</li>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Visibilité accrue</li>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Badge Pro</li>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Support prioritaire</li>
            <?php else: ?>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Tous les avantages Pro</li>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Mise en avant sur la page d'accueil</li>
                <li><i class="ri-check-line text-green-600 mr-2"></i> Analyse de profil</li>
            <?php endif; ?>
        </ul>
        <p class="mt-4 font-bold">Prix: <?= $plan_price ?> DH / mois</p>
    </div>
    
    <form method="POST" action="subscribe.php?plan=<?= $plan ?>">
        <h3 class="text-xl font-bold mb-4">Informations de paiement</h3>
        
        <div class="form-group">
            <label for="card_number">Numéro de carte</label>
            <input type="text" id="card_number" name="card_number" placeholder="1234 5678 9012 3456" maxlength="16" required>
        </div>
        
        <div class="form-group">
            <label for="card_name">Nom sur la carte</label>
            <input type="text" id="card_name" name="card_name" placeholder="John Doe" required>
        </div>
        
        <div class="form-grid">
            <div class="form-group">
                <label for="expiry_date">Date d'expiration</label>
                <input type="text" id="expiry_date" name="expiry_date" placeholder="MM/YY" maxlength="5" required>
            </div>
            
            <div class="form-group">
                <label for="cvv">CVV</label>
                <input type="text" id="cvv" name="cvv" placeholder="123" maxlength="4" required>
            </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600">
            <p>En cliquant sur "Confirmer l'abonnement", vous acceptez les <a href="#" class="text-blue-600 hover:underline">conditions d'utilisation</a> et la <a href="#" class="text-blue-600 hover:underline">politique de confidentialité</a> de SawbLi.</p>
            <p class="mt-2">Votre abonnement sera automatiquement renouvelé chaque mois. Vous pouvez annuler à tout moment.</p>
        </div>
        
        <div class="form-buttons mt-6">
            <a href="<?= $user['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php' ?>" class="btn btn-secondary btn-uniform">
                <i class="ri-arrow-left-line"></i>
                <span>Annuler</span>
            </a>
            <button type="submit" name="confirm_subscription" class="btn btn-primary btn-uniform">
                <i class="ri-check-line"></i>
                <span>Confirmer l'abonnement</span>
            </button>
        </div>
    </form>
</div>

<style>
/* ========================================
   SUBSCRIBE PAGE STYLES & BUTTON UNIFORMITY
   ======================================== */

/* Container Adaptation for Desktop */
.form-container {
    max-width: 800px;
    margin: 80px auto 2rem;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

/* Uniform Button Styles */
.btn-uniform {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    min-height: 50px;
    white-space: nowrap;
}

.btn-uniform i {
    font-size: 1.1rem;
}

.btn-uniform span {
    flex: 1;
    text-align: center;
}

/* Primary Button */
.btn-primary.btn-uniform {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(19, 64, 116, 0.3);
}

.btn-primary.btn-uniform:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(19, 64, 116, 0.4);
    text-decoration: none;
    color: white;
}

/* Secondary Button */
.btn-secondary.btn-uniform {
    background: #f8f9fa;
    color: var(--text-color);
    border: 2px solid #e1e5e9;
}

.btn-secondary.btn-uniform:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Form Buttons Container */
.form-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e1e5e9;
}

/* Subscription Details Card */
.bg-blue-50 {
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    border: 1px solid #e1e5e9;
    border-left: 4px solid var(--primary-color);
}

.bg-blue-50 h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
}

.bg-blue-50 ul li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 1rem;
    color: var(--text-color);
}

.bg-blue-50 ul li i {
    color: #28a745;
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
    font-size: 1rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(19, 64, 116, 0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Alert Styles */
.bg-red-100 {
    background: #fee;
    border-left: 4px solid #e53e3e;
    color: #c53030;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.bg-yellow-100 {
    background: #fffbeb;
    border-left: 4px solid #f59e0b;
    color: #92400e;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

/* Page Title */
.form-container h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e1e5e9;
}

/* Terms and Conditions */
.text-sm {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #666;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid var(--secondary-color);
}

.text-sm a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.text-sm a:hover {
    text-decoration: underline;
}

/* ========================================
   RESPONSIVE DESIGN FOR DESKTOP ADAPTATION
   ======================================== */

/* Large Desktop */
@media (min-width: 1200px) {
    .form-container {
        max-width: 900px;
        padding: 3rem;
        margin: 100px auto 3rem;
    }

    .btn-uniform {
        min-width: 220px;
        padding: 1.2rem 2.5rem;
        font-size: 1.1rem;
    }

    .form-buttons {
        gap: 2rem;
    }
}

/* Standard Desktop */
@media (min-width: 992px) and (max-width: 1199px) {
    .form-container {
        max-width: 800px;
        padding: 2.5rem;
        margin: 90px auto 2.5rem;
    }

    .btn-uniform {
        min-width: 200px;
        padding: 1rem 2rem;
    }
}

/* Tablet */
@media (min-width: 768px) and (max-width: 991px) {
    .form-container {
        max-width: 700px;
        padding: 2rem;
        margin: 80px auto 2rem;
    }

    .form-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-uniform {
        width: 100%;
        min-width: auto;
    }
}

/* Mobile */
@media (max-width: 767px) {
    .form-container {
        margin: 60px 1rem 1rem;
        padding: 1.5rem;
    }

    .form-container h2 {
        font-size: 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-uniform {
        width: 100%;
        min-width: auto;
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }

    .bg-blue-50 {
        padding: 1.5rem;
    }

    .bg-blue-50 h3 {
        font-size: 1.2rem;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .form-container {
        margin: 50px 0.5rem 0.5rem;
        padding: 1rem;
    }

    .btn-uniform {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        min-height: 44px;
    }

    .btn-uniform i {
        font-size: 1rem;
    }
}

/* Focus and Accessibility */
.btn-uniform:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading State */
.btn-uniform.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-uniform.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ========================================
   HEADER DESKTOP ADAPTATION OVERRIDES
   ======================================== */

/* Ensure header is properly adapted for desktop screens */
@media (min-width: 1200px) {
    .professional-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    .header-content,
    .top-bar-content {
        max-width: 1600px !important;
        margin: 0 auto !important;
        padding: 0 3rem !important;
    }

    .search-section {
        max-width: 500px !important;
        display: flex !important;
    }

    .main-navigation {
        display: block !important;
    }

    .nav-menu {
        display: flex !important;
        gap: 0.75rem !important;
    }

    .nav-link {
        padding: 0.6rem 1rem !important;
        font-size: 0.9rem !important;
        display: flex !important;
        align-items: center !important;
    }

    .nav-link span {
        display: inline !important;
    }

    .logo-main {
        font-size: 1.4rem !important;
    }

    .logo-tagline {
        font-size: 0.65rem !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    body {
        padding-top: 70px !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .professional-header {
        position: fixed !important;
        width: 100% !important;
    }

    .header-content,
    .top-bar-content {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 2rem !important;
    }

    .search-section {
        max-width: 400px !important;
        display: flex !important;
    }

    .main-navigation {
        display: block !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    body {
        padding-top: 65px !important;
    }
}

@media (min-width: 769px) and (max-width: 991px) {
    .professional-header {
        position: fixed !important;
        width: 100% !important;
    }

    .header-content {
        padding: 0 1.5rem !important;
    }

    .search-section {
        max-width: 300px !important;
        display: flex !important;
    }

    .main-navigation {
        display: block !important;
    }

    .nav-link span {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    body {
        padding-top: 60px !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
