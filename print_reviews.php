<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// التأكد من أن المستخدم هو "أدمن"
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

// استرجاع المراجعات
$filter = isset($_GET['filterUser']) ? intval($_GET['filterUser']) : null;
$query = "SELECT r.*, f.username AS from_user, t.username AS to_user FROM reviews r JOIN users f ON r.reviewer_id = f.id JOIN users t ON r.reviewee_id = t.id";
if ($filter) {
    $query .= " WHERE r.reviewee_id = $filter";
}
$query .= " ORDER BY r.created_at DESC";
$reviews = $conn->query($query)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Impression des Avis - SawbLi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
        }
    </style>
</head>
<body>

    <h2>Liste des Avis</h2>

    <table>
        <thead>
            <tr>
                <th>De</th>
                <th>À</th>
                <th>Note</th>
                <th>Commentaire</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($reviews as $rev): ?>
                <tr>
                    <td><?= htmlspecialchars($rev['from_user']) ?></td>
                    <td><?= htmlspecialchars($rev['to_user']) ?></td>
                    <td><?= $rev['rating'] ?>/5</td>
                    <td><?= htmlspecialchars($rev['comment']) ?></td>
                    <td><?= $rev['created_at'] ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <button onclick="window.print()">Imprimer cette page</button>

</body>
</html>
