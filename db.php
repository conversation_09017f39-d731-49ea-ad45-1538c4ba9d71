<?php
$host = 'localhost';
$dbname = 'sawbli';
$username = 'root';
$password = '';


try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Helper function to sanitize input data
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars_decode($data);
    return $data;
}

// Helper function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Helper function to get current user data
function getCurrentUser($conn) {
    if (isLoggedIn()) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    return null;
}

// Helper function to verify if a user is a freelancer
function isFreelancer($conn, $user_id = null) {
    if ($user_id === null) {
        if (!isLoggedIn()) return false;
        $user_id = $_SESSION['user_id'];
    }
    
    $stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    return ($user && $user['user_type'] === 'freelancer');
}

// Helper function to verify if a user is a client
function isClient($conn, $user_id = null) {
    if ($user_id === null) {
        if (!isLoggedIn()) return false;
        $user_id = $_SESSION['user_id'];
    }
    
    $stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    return ($user && $user['user_type'] === 'client');
}

// Helper function to redirect with error
function redirectWithError($location, $error) {
    $_SESSION['error'] = $error;
    header("Location: $location");
    exit;
}

// Helper function to redirect with success
function redirectWithSuccess($location, $success) {
    $_SESSION['success'] = $success;
    header("Location: $location");
    exit;
}

// Function to get all categories
function getCategories($conn) {
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    return $stmt->fetchAll();
}
?>
