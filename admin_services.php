<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Vérifier si l'utilisateur est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

include 'includes/admin_header.php';

// Récupérer les services
$stmt = $conn->query("SELECT id, title, description, price FROM services ORDER BY id DESC");
$services = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="p-6">
    <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">Gestion des Services 🛠️</h1>

    <div class="overflow-x-auto shadow-lg rounded-lg bg-white p-6">
        <table class="min-w-full table-auto text-sm text-left text-gray-700">
            <thead class="bg-gray-100 uppercase text-gray-600">
                <tr>
                    <th class="px-6 py-3">#</th>
                    <th class="px-6 py-3">Titre</th>
                    <th class="px-6 py-3">Description</th>
                    <th class="px-6 py-3">Prix</th>
                    <th class="px-6 py-3 text-right">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                <?php if (!empty($services)): ?>
                    <?php foreach ($services as $index => $service): ?>
                        <tr class="hover:bg-gray-100 transition">
                            <td class="px-6 py-4 font-medium"><?= $index + 1 ?></td>
                            <td class="px-6 py-4"><?= htmlspecialchars($service['title']) ?></td>
                            <td class="px-6 py-4"><?= htmlspecialchars($service['description']) ?></td>
                            <td class="px-6 py-4"><?= htmlspecialchars($service['price']) ?> MAD</td>
                            <td class="px-6 py-4 text-right space-x-2">
                                <a href="view_service.php?id=<?= $service['id'] ?>" class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                                    Voir détails
                                </a>
                                <a href="delete_service.php?id=<?= $service['id'] ?>" 
                                   onclick="return confirm('Voulez-vous vraiment supprimer ce service ?');" 
                                   class="inline-block bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition">
                                    Supprimer
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="5" class="text-center py-4 text-gray-500">Aucun service trouvé.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
