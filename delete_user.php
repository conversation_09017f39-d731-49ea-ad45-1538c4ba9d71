<?php
session_start();
require_once 'db.php';
$conn = $pdo;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['user_id']) && $_SESSION['user_type'] === 'admin') {
    $user_id = (int) $_POST['user_id'];

    // ما تحذفش الأدمن نفسه
    if ($_SESSION['user_id'] == $user_id) {
        header("Location: admin_users.php?error=not_allowed");
        exit;
    }

    // حذف المستخدم
    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
}

header("Location: admin_users.php");
exit;
