<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Vérification si l'utilisateur est administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

include 'includes/admin_header.php';

// Gestion de la recherche et des filtres
$searchTerm = isset($_GET['search']) ? "%" . $_GET['search'] . "%" : '%';
$typeFilter = isset($_GET['type']) ? $_GET['type'] : '';

// Requête pour obtenir les utilisateurs selon les filtres
$query = "SELECT id, username, email, user_type, full_name, profile_picture FROM users WHERE username LIKE :searchTerm";
if ($typeFilter !== '') {
    $query .= " AND user_type = :typeFilter";
}
$query .= " ORDER BY id DESC LIMIT 10"; // Limite pour la pagination

$stmt = $conn->prepare($query);
$stmt->bindParam(':searchTerm', $searchTerm, PDO::PARAM_STR);
if ($typeFilter !== '') {
    $stmt->bindParam(':typeFilter', $typeFilter, PDO::PARAM_STR);
}
$stmt->execute();
$users = $stmt->fetchAll();
?>

<h2 class="text-2xl font-bold mb-6">Gestion des utilisateurs</h2>

<!-- Formulaire de recherche et filtre -->
<div class="mb-4 flex justify-between items-center">
    <form method="GET" class="flex items-center space-x-2">
        <input type="text" name="search" placeholder="Rechercher par nom d'utilisateur..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" class="px-4 py-2 border rounded-lg">
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg">Chercher</button>
    </form>
    <form method="GET" class="flex items-center space-x-2">
        <select name="type" class="px-4 py-2 border rounded-lg" onchange="this.form.submit()">
            <option value="">Tous les types</option>
            <option value="freelancer" <?= isset($_GET['type']) && $_GET['type'] === 'freelancer' ? 'selected' : '' ?>>Freelancer</option>
            <option value="client" <?= isset($_GET['type']) && $_GET['type'] === 'client' ? 'selected' : '' ?>>Client</option>
        </select>
    </form>
</div>

<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table class="min-w-full text-sm text-left">
        <thead class="bg-gray-100 text-gray-700 uppercase">
            <tr>
                <th class="px-6 py-3">#</th>
                <th class="px-6 py-3">Photo</th>
                <th class="px-6 py-3">Nom complet</th>
                <th class="px-6 py-3">Nom d'utilisateur</th>
                <th class="px-6 py-3">Email</th>
                <th class="px-6 py-3">Type</th>
                <th class="px-6 py-3 text-right">Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $index => $user): ?>
                <tr class="border-t hover:bg-gray-50">
                    <td class="px-6 py-4"><?= $index + 1 ?></td>
                    <td class="px-6 py-4">
                        <?php
                            $relativePath = !empty($user['profile_picture']) ? 'uploads/profile_pictures/' . $user['profile_picture'] : 'uploads/default.png';
                        ?>
                        <img src="<?= htmlspecialchars($relativePath) ?>" alt="Photo de profil" class="w-10 h-10 rounded-full object-cover border">
                    </td>
                    <td class="px-6 py-4"><?= htmlspecialchars($user['full_name']) ?></td>
                    <td class="px-6 py-4"><?= htmlspecialchars($user['username']) ?></td>
                    <td class="px-6 py-4"><?= htmlspecialchars($user['email']) ?></td>
                    <td class="px-6 py-4 capitalize"><?= htmlspecialchars($user['user_type']) ?></td>
                    <td class="px-6 py-4 text-right">
                        <!-- Actions pour l'utilisateur -->
                        <a href="edit_user.php?id=<?= $user['id'] ?>" class="text-blue-600 hover:underline">Modifier</a> |
                        <form method="POST" action="delete_user.php" class="inline-block" onsubmit="return confirm('Voulez-vous vraiment supprimer cet utilisateur ?');">
                            <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                            <button type="submit" class="text-red-600 hover:underline">Supprimer</button>
                            <a href="user_details.php?user_id=<?= $user['id'] ?>" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 transition duration-200">Voir Détails</a>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="mt-4 flex justify-between items-center">
    <div class="text-sm text-gray-500">
        Page 1 sur 5
    </div>
    <div>
        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg">Précédent</button>
        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg">Suivant</button>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
