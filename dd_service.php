<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if service ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID de service invalide.";
    header("Location: browse_services.php");
    exit;
}

$service_id = intval($_GET['id']);

// Get detailed service data with freelancer information
$stmt = $conn->prepare("
    SELECT s.*, u.username as freelancer_username, u.full_name as freelancer_name, 
           u.id as freelancer_id, u.profile_picture, u.bio, u.is_pro,
           u.created_at as member_since
    FROM services s
    JOIN users u ON s.user_id = u.id
    WHERE s.id = ? AND s.status = 'active'
");
$stmt->execute([$service_id]);
$service = $stmt->fetch();

if (!$service) {
    $_SESSION['error'] = "Service introuvable ou inactif.";
    header("Location: browse_services.php");
    exit;
}

// Get freelancer statistics
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT p.id) as total_projects,
        COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as completed_projects,
        AVG(CASE WHEN r.rating IS NOT NULL THEN r.rating END) as avg_rating,
        COUNT(DISTINCT r.id) as total_reviews,
        COUNT(DISTINCT s.id) as total_services
    FROM users u
    LEFT JOIN projects p ON u.id = p.freelancer_id
    LEFT JOIN reviews r ON u.id = r.reviewee_id
    LEFT JOIN services s ON u.id = s.user_id AND s.status = 'active'
    WHERE u.id = ?
    GROUP BY u.id
");
$stmt->execute([$service['freelancer_id']]);
$freelancer_stats = $stmt->fetch();

// Get recent reviews for this freelancer
$stmt = $conn->prepare("
    SELECT r.*, p.title as project_title, u.username as reviewer_name, u.full_name as reviewer_full_name
    FROM reviews r
    JOIN projects p ON r.project_id = p.id
    JOIN users u ON r.reviewer_id = u.id
    WHERE r.reviewee_id = ?
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$service['freelancer_id']]);
$recent_reviews = $stmt->fetchAll();

// Get similar services in the same category
$stmt = $conn->prepare("
    SELECT s.*, u.username as freelancer_username, u.full_name as freelancer_name
    FROM services s
    JOIN users u ON s.user_id = u.id
    WHERE s.category = ? AND s.id != ? AND s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 4
");
$stmt->execute([$service['category'], $service_id]);
$similar_services = $stmt->fetchAll();

// Handle service booking
$booking_error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['book_service'])) {
    if (!isset($_SESSION['user_id'])) {
        $booking_error = "Vous devez être connecté pour réserver un service.";
    } elseif ($_SESSION['user_type'] !== 'client') {
        $booking_error = "Seuls les clients peuvent réserver des services.";
    } elseif ($_SESSION['user_id'] == $service['freelancer_id']) {
        $booking_error = "Vous ne pouvez pas réserver votre propre service.";
    } else {
        $project_title = sanitize($_POST['project_title'] ?? '');
        $project_description = sanitize($_POST['project_description'] ?? '');
        $deadline = $_POST['deadline'] ?? null;
        
        if (empty($project_title) || empty($project_description)) {
            $booking_error = "Veuillez remplir tous les champs requis.";
        } else {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO projects (title, description, client_id, freelancer_id, service_id, price, deadline, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
                ");
                $stmt->execute([
                    $project_title,
                    $project_description,
                    $_SESSION['user_id'],
                    $service['freelancer_id'],
                    $service_id,
                    $service['price'],
                    $deadline
                ]);
                
                $_SESSION['success'] = "Votre demande de projet a été envoyée avec succès!";
                header("Location: client_dashboard.php");
                exit;
            } catch (PDOException $e) {
                $booking_error = "Erreur lors de la réservation: " . $e->getMessage();
            }
        }
    }
}

include 'includes/header.php';
?>

<style>
.service-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    margin-top: 100px;
}

.service-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.service-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.service-content {
    position: relative;
    z-index: 1;
}

.service-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.service-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.service-price {
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #f0f0f0;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

.freelancer-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.freelancer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    border: 4px solid white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.pro-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-top: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.booking-form {
    background: #f8f9ff;
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid #667eea;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input, .form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.review-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reviewer-name {
    font-weight: 600;
    color: #333;
}

.rating-stars {
    color: #ffd700;
    font-size: 1.1rem;
}

.similar-services {
    margin-top: 3rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.alert-error {
    background: #fee;
    color: #c33;
    border: 1px solid #fcc;
}

/* Mobile First Responsive Design */
@media (max-width: 480px) {
    .service-detail-container {
        padding: 1rem 0.5rem;
        margin-top: 80px;
    }

    .service-header {
        padding: 2rem 1rem;
        border-radius: 15px;
        margin-bottom: 1.5rem;
    }

    .service-title {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .service-meta {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .meta-item {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    .service-price {
        font-size: 1.5rem;
        margin-top: 1rem;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .content-section {
        padding: 1.5rem;
        border-radius: 12px;
    }

    .section-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .freelancer-card {
        padding: 1.5rem;
        border-radius: 12px;
    }

    .freelancer-avatar {
        width: 60px;
        height: 60px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .stat-item {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .booking-form {
        padding: 1.5rem;
        border-radius: 12px;
    }

    .form-input, .form-textarea {
        padding: 0.6rem;
        font-size: 1rem;
    }

    .btn-primary {
        padding: 0.8rem 1.5rem;
        font-size: 0.95rem;
    }

    .review-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .service-card {
        padding: 1rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .service-detail-container {
        padding: 1.5rem 1rem;
        margin-top: 90px;
    }

    .service-header {
        padding: 2.5rem 1.5rem;
    }

    .service-title {
        font-size: 2.2rem;
    }

    .service-meta {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .service-detail-container {
        padding: 2rem 1.5rem;
        margin-top: 100px;
    }

    .main-content {
        grid-template-columns: 1.5fr 1fr;
        gap: 2rem;
    }

    .service-title {
        font-size: 2.3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    .main-content {
        grid-template-columns: 2fr 1fr;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}
</style>

<div class="service-detail-container">
    <!-- Service Header -->
    <div class="service-header">
        <div class="service-content">
            <h1 class="service-title"><?= htmlspecialchars($service['title']) ?></h1>
            
            <div class="service-meta">
                <div class="meta-item">
                    <i class="ri-folder-line"></i>
                    <span><?= htmlspecialchars($service['category']) ?></span>
                </div>
                <div class="meta-item">
                    <i class="ri-time-line"></i>
                    <span>Publié le <?= date('d/m/Y', strtotime($service['created_at'])) ?></span>
                </div>
                <div class="meta-item">
                    <i class="ri-eye-line"></i>
                    <span>Service actif</span>
                </div>
            </div>
            
            <div class="service-price"><?= number_format($service['price'], 2) ?> DH</div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-content">
        <!-- Left Column - Service Details -->
        <div>
            <!-- Service Description -->
            <div class="content-section">
                <h2 class="section-title">Description du Service</h2>
                <div class="service-description">
                    <?= nl2br(htmlspecialchars($service['description'])) ?>
                </div>
            </div>

            <!-- Reviews Section -->
            <?php if (!empty($recent_reviews)): ?>
            <div class="content-section" style="margin-top: 2rem;">
                <h2 class="section-title">Avis Récents</h2>
                <?php foreach ($recent_reviews as $review): ?>
                <div class="review-item">
                    <div class="review-header">
                        <span class="reviewer-name"><?= htmlspecialchars($review['reviewer_full_name']) ?></span>
                        <div class="rating-stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <?= $i <= $review['rating'] ? '★' : '☆' ?>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <p><?= htmlspecialchars($review['comment']) ?></p>
                    <small class="text-gray-500">Projet: <?= htmlspecialchars($review['project_title']) ?></small>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Right Column - Freelancer Info & Booking -->
        <div>
            <!-- Freelancer Card -->
            <div class="freelancer-card">
                <?php if ($service['profile_picture']): ?>
                    <img src="<?= htmlspecialchars($service['profile_picture']) ?>" alt="<?= htmlspecialchars($service['freelancer_name']) ?>" class="freelancer-avatar">
                <?php else: ?>
                    <div class="freelancer-avatar" style="background: #667eea; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                        <?= strtoupper(substr($service['freelancer_name'], 0, 1)) ?>
                    </div>
                <?php endif; ?>
                
                <h3><?= htmlspecialchars($service['freelancer_name']) ?></h3>
                <p class="text-gray-600">@<?= htmlspecialchars($service['freelancer_username']) ?></p>
                
                <?php if ($service['is_pro']): ?>
                    <span class="pro-badge">✨ SawbLi Pro</span>
                <?php endif; ?>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number"><?= $freelancer_stats['completed_projects'] ?? 0 ?></div>
                        <div class="stat-label">Projets terminés</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $freelancer_stats['avg_rating'] ? number_format($freelancer_stats['avg_rating'], 1) : 'N/A' ?></div>
                        <div class="stat-label">Note moyenne</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $freelancer_stats['total_reviews'] ?? 0 ?></div>
                        <div class="stat-label">Avis reçus</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $freelancer_stats['total_services'] ?? 0 ?></div>
                        <div class="stat-label">Services actifs</div>
                    </div>
                </div>
                
                <?php if ($service['bio']): ?>
                    <p style="margin-top: 1rem; font-style: italic; color: #666;">
                        "<?= htmlspecialchars(substr($service['bio'], 0, 100)) ?><?= strlen($service['bio']) > 100 ? '...' : '' ?>"
                    </p>
                <?php endif; ?>
                
                <p style="margin-top: 1rem; font-size: 0.9rem; color: #888;">
                    Membre depuis <?= date('M Y', strtotime($service['member_since'])) ?>
                </p>
            </div>

            <!-- Booking Form -->
            <?php if (isset($_SESSION['user_id']) && $_SESSION['user_type'] === 'client' && $_SESSION['user_id'] !== $service['freelancer_id']): ?>
                <div class="booking-form">
                    <h3 style="margin-bottom: 1.5rem; color: #333;">Réserver ce Service</h3>
                    
                    <?php if (!empty($booking_error)): ?>
                        <div class="alert alert-error">
                            <?= $booking_error ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">Titre du Projet *</label>
                            <input type="text" name="project_title" class="form-input" required 
                                   placeholder="Ex: Création d'un site web e-commerce">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Description du Projet *</label>
                            <textarea name="project_description" class="form-textarea" rows="4" required 
                                      placeholder="Décrivez votre projet en détail..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Date limite souhaitée</label>
                            <input type="date" name="deadline" class="form-input" 
                                   min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                        </div>
                        
                        <button type="submit" name="book_service" class="btn-primary">
                            <i class="ri-send-plane-line"></i> Envoyer la Demande
                        </button>
                    </form>
                </div>
            <?php elseif (isset($_SESSION['user_id']) && $_SESSION['user_id'] === $service['freelancer_id']): ?>
                <div class="alert" style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7;">
                    <p><strong>C'est votre service!</strong></p>
                    <p>Vous pouvez le <a href="edit_service.php?id=<?= $service_id ?>" style="color: #667eea; text-decoration: underline;">modifier</a> si nécessaire.</p>
                </div>
            <?php else: ?>
                <div class="alert" style="background: #e3f2fd; color: #1565c0; border: 1px solid #bbdefb;">
                    <p><strong>Connectez-vous pour réserver</strong></p>
                    <p><a href="login.php" style="color: #667eea; text-decoration: underline;">Connectez-vous</a> en tant que client pour réserver ce service.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Similar Services -->
    <?php if (!empty($similar_services)): ?>
    <div class="similar-services">
        <h2 class="section-title">Services Similaires</h2>
        <div class="services-grid">
            <?php foreach ($similar_services as $similar): ?>
            <a href="dd_service.php?id=<?= $similar['id'] ?>" class="service-card">
                <h4 style="margin-bottom: 1rem; color: #333;"><?= htmlspecialchars($similar['title']) ?></h4>
                <p style="color: #666; font-size: 0.9rem; margin-bottom: 1rem;">
                    <?= htmlspecialchars(substr($similar['description'], 0, 100)) ?>...
                </p>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #888; font-size: 0.8rem;">Par <?= htmlspecialchars($similar['freelancer_name']) ?></span>
                    <span style="font-weight: 600; color: #667eea;"><?= number_format($similar['price'], 2) ?> DH</span>
                </div>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
