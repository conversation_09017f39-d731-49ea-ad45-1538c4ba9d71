<?php
session_start();
require_once 'db.php';
$conn = $pdo;

if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

// Récupération des données statistiques
$userCount = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
$freelancerCount = $conn->query("SELECT COUNT(*) FROM users WHERE user_type = 'freelancer'")->fetchColumn();
$serviceCount = $conn->query("SELECT COUNT(*) FROM services")->fetchColumn();
$messageCount = $conn->query("SELECT COUNT(*) FROM messages")->fetchColumn();
$transactionCount = $conn->query("SELECT COUNT(*) FROM transactions")->fetchColumn();

// Dernières transactions
$latestTransactions = $conn->query("
    SELECT t.*, u.username 
    FROM transactions AS t
    JOIN users AS u ON t.user_id = u.id
    ORDER BY t.created_at DESC
    LIMIT 5
")->fetchAll(PDO::FETCH_ASSOC);

// Derniers utilisateurs enregistrés par mois
$monthlyUsers = $conn->query("SELECT DATE_FORMAT(created_at, '%Y-%m') AS month, COUNT(*) AS count FROM users GROUP BY month ORDER BY month ASC")->fetchAll(PDO::FETCH_ASSOC);

// Derniers avis
$latestReviews = $conn->query("
    SELECT r.*, f.username AS from_user, t.username AS to_user 
    FROM reviews r 
    JOIN users f ON r.reviewer_id = f.id 
    JOIN users t ON r.reviewee_id = t.id 
    ORDER BY r.created_at DESC 
    LIMIT 5
")->fetchAll(PDO::FETCH_ASSOC);

// Utilisateurs ayant reçu des avis
$reviewUsers = $conn->query("SELECT u.id, u.username FROM users u JOIN reviews r ON u.id = r.reviewee_id GROUP BY u.id, u.username")->fetchAll(PDO::FETCH_ASSOC);

// Moyennes des avis
$averageRatings = $conn->query("
    SELECT u.username, AVG(r.rating) AS average_rating, COUNT(r.id) AS review_count 
    FROM reviews r 
    JOIN users u ON r.reviewee_id = u.id 
    GROUP BY r.reviewee_id 
    ORDER BY average_rating DESC 
    LIMIT 5
")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Admin - SawbLi</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex">
<aside class="w-64 bg-white p-6 hidden md:block shadow">
    <h1 class="text-2xl font-bold text-blue-600 mb-8">SawbLi Admin</h1>
    <nav class="space-y-4">
        <a href="admin_dashboard.php" class="block text-gray-700 font-medium hover:text-blue-600">📊 Dashboard</a>
        <a href="admin_users.php" class="block text-gray-700 font-medium hover:text-blue-600">👤 Utilisateurs</a>
        <a href="admin_services.php" class="block text-gray-700 font-medium hover:text-blue-600">💼 Services</a>
        <a href="admin_messages.php" class="block text-gray-700 font-medium hover:text-blue-600">✉️ Messages</a>
        <a href="logout.php" class="block text-red-500 font-medium hover:text-red-700">🚪 Déconnexion</a>
    </nav>
</aside>

<main class="flex-1 p-6 space-y-6">
    <h2 class="text-3xl font-bold mb-4">Bienvenue, Admin 👋</h2>

    <!-- Statistiques -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div class="bg-white p-4 rounded-xl shadow text-center">
            <p class="text-sm text-gray-500">Utilisateurs</p>
            <p class="text-2xl font-bold text-blue-600"><?= $userCount; ?></p>
        </div>
        <div class="bg-white p-4 rounded-xl shadow text-center">
            <p class="text-sm text-gray-500">Freelancers</p>
            <p class="text-2xl font-bold text-green-600"><?= $freelancerCount; ?></p>
        </div>
        <div class="bg-white p-4 rounded-xl shadow text-center">
            <p class="text-sm text-gray-500">Services</p>
            <p class="text-2xl font-bold text-purple-600"><?= $serviceCount; ?></p>
        </div>
        <div class="bg-white p-4 rounded-xl shadow text-center">
            <p class="text-sm text-gray-500">Messages</p>
            <p class="text-2xl font-bold text-orange-500"><?= $messageCount; ?></p>
        </div>
        <div class="bg-white p-4 rounded-xl shadow text-center">
            <p class="text-sm text-gray-500">Transactions</p>
            <p class="text-2xl font-bold text-teal-500"><?= $transactionCount; ?></p>
        </div>
    </div>

    <!-- Graph des utilisateurs mensuels -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white p-6 rounded-xl shadow">
            <h3 class="text-lg font-bold mb-4">📈 Nouveaux Utilisateurs</h3>
            <canvas id="userChart"></canvas>
        </div>

        <!-- Dernières Transactions -->
        <div class="bg-white p-6 rounded-xl shadow">
            <h3 class="text-lg font-bold mb-4">💳 Dernières Transactions</h3>
            <ul class="space-y-3">
                <?php foreach ($latestTransactions as $txn): ?>
                    <li class="flex justify-between text-sm text-gray-700">
                        <span><?= htmlspecialchars($txn['username']) ?> a <?= $txn['transaction_type'] === 'income' ? 'reçu' : 'envoyé' ?> <?= $txn['amount'] ?> DH</span>
                        <span class="text-gray-400 text-xs"><?= $txn['created_at'] ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>

    <!-- Derniers Avis -->
    <div class="bg-white p-6 rounded-xl shadow">
        <h3 class="text-lg font-bold mb-4">🌟 Derniers Avis</h3>
        <table class="min-w-full text-sm">
            <thead class="text-left text-gray-500 border-b">
                <tr>
                    <th>De</th><th>À</th><th>Note</th><th>Commentaire</th><th>Date</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($latestReviews as $rev): ?>
                    <tr class="border-b">
                        <td><?= htmlspecialchars($rev['from_user']) ?></td>
                        <td><?= htmlspecialchars($rev['to_user']) ?></td>
                        <td><?= $rev['rating'] ?>/5</td>
                        <td><?= htmlspecialchars($rev['comment']) ?></td>
                        <td><?= $rev['created_at'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Moyennes des Avis -->
    <div class="bg-white p-6 rounded-xl shadow">
        <h3 class="text-lg font-bold mb-4">📊 Moyennes des Avis</h3>
        <table class="min-w-full text-sm">
            <thead class="text-left text-gray-500 border-b">
                <tr>
                    <th>Utilisateur</th>
                    <th>Note Moyenne</th>
                    <th>Nombre d’Avis</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($averageRatings as $avg): ?>
                    <tr class="border-b">
                        <td><?= htmlspecialchars($avg['username']) ?></td>
                        <td><?= number_format($avg['average_rating'], 2) ?>/5</td>
                        <td><?= $avg['review_count'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</main>

<script>
// Graph des utilisateurs
const userChart = new Chart(document.getElementById('userChart'), {
    type: 'line',
    data: {
        labels: <?= json_encode(array_column($monthlyUsers, 'month')) ?>,
        datasets: [{
            label: 'Nouveaux Utilisateurs',
            data: <?= json_encode(array_column($monthlyUsers, 'count')) ?>,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {legend: {display: false}},
        scales: {
            y: {beginAtZero: true}
        }
    }
});
</script>
</body>
</html>
