<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$receiver_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Pagination setup
$limit = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// Get total messages count
$totalStmt = $conn->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ?");
$totalStmt->execute([$receiver_id]);
$totalMessages = $totalStmt->fetchColumn();
$totalPages = ceil($totalMessages / $limit);

// Fetch messages
$stmt = $conn->prepare("SELECT m.*, u.username AS sender_name FROM messages m JOIN users u ON m.sender_id = u.id WHERE m.receiver_id = ? ORDER BY m.sent_at DESC LIMIT ? OFFSET ?");
$stmt->bindValue(1, $receiver_id, PDO::PARAM_INT);
$stmt->bindValue(2, $limit, PDO::PARAM_INT);
$stmt->bindValue(3, $offset, PDO::PARAM_INT);
$stmt->execute();
$messages = $stmt->fetchAll();

// Handle reply
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['reply_to'])) {
    $reply_to = (int)$_POST['reply_to'];
    $reply_msg = sanitize($_POST['reply_message']);

    // Get sender ID from original message
    $replyStmt = $conn->prepare("SELECT sender_id FROM messages WHERE id = ? AND receiver_id = ?");
    $replyStmt->execute([$reply_to, $receiver_id]);
    $original = $replyStmt->fetch();

    if ($original) {
        $stmt = $conn->prepare("INSERT INTO messages (sender_id, receiver_id, message, sent_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$receiver_id, $original['sender_id'], $reply_msg]);
        $success = "Message envoyé avec succès.";
    } else {
        $error = "Message original introuvable.";
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Messages Reçus</h1>

    <?php if ($error): ?>
        <div class="bg-red-100 text-red-700 p-4 mb-4 rounded"> <?= $error ?> </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="bg-green-100 text-green-700 p-4 mb-4 rounded"> <?= $success ?> </div>
    <?php endif; ?>

    <?php foreach ($messages as $msg): ?>
        <div class="bg-white shadow rounded p-4 mb-4">
            <p class="text-sm text-gray-600">De: <strong><?= htmlspecialchars($msg['sender_name']) ?></strong> — <?= $msg['sent_at'] ?></p>
            <p class="my-2 text-gray-800"><?= nl2br(htmlspecialchars($msg['message'])) ?></p>

            <!-- Reply Form -->
            <form method="POST" class="mt-2">
                <input type="hidden" name="reply_to" value="<?= $msg['id'] ?>">
                <textarea name="reply_message" rows="2" placeholder="Écrire une réponse..." class="w-full p-2 border rounded mb-2"></textarea>
                <button type="submit" class="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700">Envoyer</button>
            </form>
        </div>
    <?php endforeach; ?>

    <!-- Pagination -->
    <div class="flex justify-center space-x-2 mt-6">
        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <a href="?page=<?= $i ?>" class="px-3 py-1 rounded <?= $i == $page ? 'bg-blue-600 text-white' : 'bg-gray-200' ?>"> <?= $i ?> </a>
        <?php endfor; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
