<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Menu</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            padding-top: 70px;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 15px 20px;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }

        .main-navigation {
            display: block;
        }

        .main-navigation ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        .main-navigation a {
            text-decoration: none;
            color: #333;
            padding: 10px 15px;
        }

        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 44px;
            min-height: 44px;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu-toggle:hover {
            background: rgba(37, 99, 235, 0.1);
        }

        .hamburger-line {
            width: 24px;
            height: 3px;
            background: #2563eb;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        .mobile-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-nav-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-nav-container {
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            max-width: 85vw;
            height: 100vh;
            background: white;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .mobile-nav-overlay.active .mobile-nav-container {
            transform: translateX(0);
        }

        .mobile-nav-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: #2563eb;
            color: white;
        }

        .mobile-nav-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .mobile-nav-menu {
            padding: 20px;
        }

        .mobile-nav-item {
            display: block;
            padding: 15px 0;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .main-navigation {
                display: none !important;
            }
            
            .mobile-menu-toggle {
                display: flex !important;
            }
        }

        .content {
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">SawbLi</div>
            
            <nav class="main-navigation">
                <ul>
                    <li><a href="#">Accueil</a></li>
                    <li><a href="#">Services</a></li>
                    <li><a href="#">À propos</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
            </nav>

            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>

    <div class="mobile-nav-overlay" id="mobile-nav">
        <div class="mobile-nav-container">
            <div class="mobile-nav-header">
                <div class="mobile-logo">SawbLi</div>
                <button class="mobile-nav-close" id="mobile-nav-close">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <nav class="mobile-nav-menu">
                <a href="#" class="mobile-nav-item">Accueil</a>
                <a href="#" class="mobile-nav-item">Services</a>
                <a href="#" class="mobile-nav-item">À propos</a>
                <a href="#" class="mobile-nav-item">Contact</a>
            </nav>
        </div>
    </div>

    <div class="content">
        <h1>Test Mobile Menu</h1>
        <p>Resize your browser to mobile size (≤768px) to see the hamburger menu.</p>
        <p>Click the hamburger menu to open the mobile navigation.</p>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Open browser developer tools (F12)</li>
            <li>Toggle device toolbar (mobile view)</li>
            <li>The hamburger menu should appear in the top right</li>
            <li>Click it to open the mobile navigation</li>
            <li>Click the X or overlay to close it</li>
        </ol>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            const mobileOverlay = document.getElementById('mobile-nav');
            const mobileClose = document.getElementById('mobile-nav-close');

            function toggleMobileMenu() {
                if (mobileOverlay && mobileToggle) {
                    const isActive = mobileOverlay.classList.contains('active');
                    
                    if (isActive) {
                        closeMobileMenu();
                    } else {
                        openMobileMenu();
                    }
                }
            }

            function openMobileMenu() {
                if (mobileOverlay && mobileToggle) {
                    mobileOverlay.classList.add('active');
                    mobileToggle.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }

            function closeMobileMenu() {
                if (mobileOverlay && mobileToggle) {
                    mobileOverlay.classList.remove('active');
                    mobileToggle.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
            }

            // Mobile menu toggle
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleMobileMenu();
                });
            }

            // Mobile menu close
            if (mobileClose) {
                mobileClose.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    closeMobileMenu();
                });
            }

            // Close mobile menu when clicking overlay
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function(e) {
                    if (e.target === mobileOverlay) {
                        closeMobileMenu();
                    }
                });
            }

            // Close mobile menu with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeMobileMenu();
                }
            });
        });
    </script>
</body>
</html>
