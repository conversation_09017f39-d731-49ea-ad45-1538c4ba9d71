<?php
session_start();
require_once 'db.php';
$conn = $pdo;
// Get all categories for filter
$categories = [];
$stmt = $conn->query("SELECT name FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Process filters
$category = isset($_GET['category']) ? sanitize($_GET['category']) : '';
$min_price = isset($_GET['min_price']) && is_numeric($_GET['min_price']) ? floatval($_GET['min_price']) : null;
$max_price = isset($_GET['max_price']) && is_numeric($_GET['max_price']) ? floatval($_GET['max_price']) : null;
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';

// Build query
$query = "
    SELECT s.*, u.username as freelancer_name, u.id as freelancer_id,
           (SELECT AVG(r.rating) FROM reviews r 
            JOIN projects p ON r.project_id = p.id 
            WHERE p.freelancer_id = u.id) as avg_rating,
           (SELECT COUNT(*) FROM projects p WHERE p.service_id = s.id AND p.status = 'completed') as completed_count
    FROM services s
    JOIN users u ON s.user_id = u.id
    WHERE s.status = 'active'
";

$params = [];

// Apply category filter
if (!empty($category)) {
    $query .= " AND s.category = ?";
    $params[] = $category;
}

// Apply price filters
if ($min_price !== null) {
    $query .= " AND s.price >= ?";
    $params[] = $min_price;
}

if ($max_price !== null) {
    $query .= " AND s.price <= ?";
    $params[] = $max_price;
}

// Apply sorting
switch ($sort) {
    case 'price_low':
        $query .= " ORDER BY s.price ASC";
        break;
    case 'price_high':
        $query .= " ORDER BY s.price DESC";
        break;
    case 'rating':
        $query .= " ORDER BY avg_rating DESC";
        break;
    case 'popularity':
        $query .= " ORDER BY completed_count DESC";
        break;
    case 'newest':
    default:
        $query .= " ORDER BY s.created_at DESC";
        break;
}

// Execute query
$stmt = $conn->prepare($query);
$stmt->execute($params);
$services = $stmt->fetchAll();

// Get total count
$total_services = count($services);

include 'includes/header.php';
?>

<div class="browse-services-container" style="max-width: 1200px; margin: 0 auto; padding: 2rem 1rem; margin-top: 100px;">
    <div class="page-header" style="text-align: center; margin-bottom: 2rem;">
        <h1 style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color); margin-bottom: 0.5rem;">Parcourir les Services</h1>
        <p style="color: var(--text-color); font-size: 1.1rem;">Découvrez les meilleurs freelances marocains</p>
    </div>

    <!-- Filter Section -->
    <div class="filter-container" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-bottom: 2rem;">
        <h3 style="font-size: 1.2rem; color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
            <i class="ri-filter-3-line"></i> Filtres de recherche
        </h3>
        <form action="browse_services.php" method="GET" class="filter-form" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
            <div class="filter-group">
                <label for="category" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--text-color);">
                    <i class="ri-folder-line"></i> Catégorie
                </label>
                <select id="category" name="category" style="width: 100%; padding: 0.75rem; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem; background: white;">
                    <option value="">Toutes les catégories</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?= htmlspecialchars($cat) ?>" <?= $category === $cat ? 'selected' : '' ?>>
                            <?= htmlspecialchars($cat) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="min_price" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--text-color);">
                    <i class="ri-money-dollar-circle-line"></i> Prix min (DH)
                </label>
                <input type="number" id="min_price" name="min_price" min="0" step="10"
                       value="<?= $min_price !== null ? $min_price : '' ?>"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem;"
                       placeholder="0">
            </div>

            <div class="filter-group">
                <label for="max_price" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--text-color);">
                    <i class="ri-money-dollar-circle-line"></i> Prix max (DH)
                </label>
                <input type="number" id="max_price" name="max_price" min="0" step="10"
                       value="<?= $max_price !== null ? $max_price : '' ?>"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem;"
                       placeholder="Illimité">
            </div>

            <div class="filter-group">
                <label for="sort" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--text-color);">
                    <i class="ri-sort-desc"></i> Trier par
                </label>
                <select id="sort" name="sort" style="width: 100%; padding: 0.75rem; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem; background: white;">
                    <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>Plus récents</option>
                    <option value="price_low" <?= $sort === 'price_low' ? 'selected' : '' ?>>Prix croissant</option>
                    <option value="price_high" <?= $sort === 'price_high' ? 'selected' : '' ?>>Prix décroissant</option>
                    <option value="rating" <?= $sort === 'rating' ? 'selected' : '' ?>>Meilleures notes</option>
                    <option value="popularity" <?= $sort === 'popularity' ? 'selected' : '' ?>>Popularité</option>
                </select>
            </div>

            <div class="filter-group">
                <button type="submit" class="btn btn-primary"
                        style="width: 100%; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%); color: white; border: none; border-radius: 8px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(19, 64, 116, 0.3)'"
                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <i class="ri-search-line"></i> Appliquer les filtres
                </button>
            </div>
        </form>
    </div>

    <!-- Results Count -->
    <div class="results-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; padding: 1rem; background: #f8f9ff; border-radius: 10px; border-left: 4px solid var(--primary-color);">
        <div>
            <p style="font-size: 1.1rem; font-weight: 600; color: var(--primary-color); margin: 0;">
                <i class="ri-search-eye-line"></i> <?= $total_services ?> service(s) trouvé(s)
                <?= !empty($category) ? 'dans <strong>' . htmlspecialchars($category) . '</strong>' : '' ?>
            </p>
        </div>
        <?php if (!empty($category) || $min_price !== null || $max_price !== null): ?>
        <div>
            <a href="browse_services.php" style="color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">
                <i class="ri-refresh-line"></i> Réinitialiser
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- Services List -->
    <?php if ($total_services > 0): ?>
        <div class="services-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 2rem;">
            <?php foreach ($services as $service): ?>
                <div class="service-card-modern" style="background: white; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease; border: 1px solid #f0f0f0;">
                    <div class="service-card-header" style="padding: 1.5rem; background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%); border-bottom: 1px solid #e1e5e9;">
                        <h3 style="font-size: 1.3rem; font-weight: 600; color: var(--text-color); margin-bottom: 0.75rem; line-height: 1.3;">
                            <?= htmlspecialchars($service['title']) ?>
                        </h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 0.5rem;">
                            <span style="font-size: 0.9rem; color: #666;">
                                <i class="ri-user-line"></i> Par:
                                <a href="profile.php?id=<?= $service['freelancer_id'] ?>"
                                   style="color: var(--primary-color); text-decoration: none; font-weight: 500;"
                                   onmouseover="this.style.textDecoration='underline'"
                                   onmouseout="this.style.textDecoration='none'">
                                    <?= htmlspecialchars($service['freelancer_name']) ?>
                                </a>
                            </span>

                            <?php if ($service['avg_rating']): ?>
                                <div style="display: flex; align-items: center; gap: 0.25rem;">
                                    <span style="color: #ffd700; font-size: 1rem;">
                                        <?php
                                        $stars = round($service['avg_rating']);
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $stars ? '★' : '☆';
                                        }
                                        ?>
                                    </span>
                                    <span style="font-size: 0.8rem; color: #666; margin-left: 0.25rem;">
                                        (<?= number_format($service['avg_rating'], 1) ?>)
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="service-card-body" style="padding: 1.5rem;">
                        <p style="color: var(--text-color); line-height: 1.6; margin-bottom: 1rem; font-size: 0.95rem;">
                            <?= nl2br(htmlspecialchars(substr($service['description'], 0, 150) . (strlen($service['description']) > 150 ? '...' : ''))) ?>
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.75rem; align-items: center;">
                            <span style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white; padding: 0.4rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500;">
                                <i class="ri-folder-line"></i> <?= htmlspecialchars($service['category']) ?>
                            </span>
                            <?php if ($service['completed_count'] > 0): ?>
                                <span style="color: #28a745; font-size: 0.8rem; font-weight: 500;">
                                    <i class="ri-check-double-line"></i> <?= $service['completed_count'] ?> projet(s) réalisé(s)
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="service-card-footer" style="padding: 1.5rem; background: #f8f9fa; display: flex; justify-content: space-between; align-items: center; border-top: 1px solid #e1e5e9;">
                        <div>
                            <span style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color);">
                                <?= number_format($service['price'], 2) ?> DH
                            </span>
                            <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">
                                <i class="ri-time-line"></i> Publié le <?= date('d/m/Y', strtotime($service['created_at'])) ?>
                            </div>
                        </div>
                        <a href="dd_service.php?id=<?= $service['id'] ?>"
                           style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; text-decoration: none; font-weight: 600; font-size: 0.9rem; transition: transform 0.2s ease, box-shadow 0.2s ease; display: inline-flex; align-items: center; gap: 0.5rem;"
                           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(19, 64, 116, 0.3)'"
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <i class="ri-eye-line"></i> Voir détails
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div style="background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%); padding: 3rem 2rem; border-radius: 20px; text-align: center; border: 2px dashed var(--secondary-color);">
            <div style="font-size: 4rem; color: var(--secondary-color); margin-bottom: 1rem;">
                <i class="ri-search-line"></i>
            </div>
            <h3 style="font-size: 1.5rem; color: var(--primary-color); margin-bottom: 1rem;">Aucun service trouvé</h3>
            <p style="color: #666; margin-bottom: 2rem; font-size: 1.1rem;">
                Essayez de modifier vos critères de recherche ou explorez d'autres catégories.
            </p>
            <a href="browse_services.php"
               style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%); color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: transform 0.2s ease;"
               onmouseover="this.style.transform='translateY(-2px)'"
               onmouseout="this.style.transform='translateY(0)'">
                <i class="ri-refresh-line"></i> Réinitialiser les filtres
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
/* Responsive improvements for browse services */
.service-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

@media (max-width: 768px) {
    .browse-services-container {
        padding: 1rem 0.5rem !important;
        margin-top: 80px !important;
    }

    .page-header h1 {
        font-size: 2rem !important;
    }

    .filter-form {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .results-header {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
    }

    .services-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .service-card-footer {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
    }

    .service-card-footer a {
        width: 100% !important;
        justify-content: center !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .filter-form {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1025px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .filter-form {
        grid-template-columns: repeat(5, 1fr) !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
