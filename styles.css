/* Import Google Font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

/* Variables de couleur */
:root {
    --primary-color: #134074;
    --secondary-color: #8DA9C4;
    --background-color: #fff;
    --text-color: #0B2545;
    --light-gray: #EEF4ED;
    --accent-color: #FFC107;
    --danger-color: #e53e3e;
    --success-color: #38a169;
    --border-color: #e2e8f0;
}

/* Reset et styles globaux */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', Arial, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    z-index: 1000;
}

/* Override for professional header - GLOBAL */
.professional-header {
    display: block !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
    background: #ffffff !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Hide old header if it exists */
.header:not(.professional-header) {
    display: none !important;
}

/* Ensure professional header components are visible on desktop */
@media (min-width: 769px) {
    .professional-header .header-wrapper {
        display: block !important;
        width: 100% !important;
    }

    .professional-header .header-top-bar {
        display: block !important;
        width: 100% !important;
    }

    .professional-header .header-main {
        display: block !important;
        width: 100% !important;
    }

    .professional-header .header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
    }

    .professional-header .logo-section {
        display: flex !important;
    }

    .professional-header .search-section {
        display: flex !important;
        flex: 1 !important;
    }

    .professional-header .main-navigation {
        display: block !important;
    }

    .professional-header .nav-menu {
        display: flex !important;
        align-items: center !important;
    }

    .professional-header .mobile-menu-toggle {
        display: none !important;
    }

    .professional-header .mobile-nav-overlay {
        display: none !important;
    }
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.dot {
    color: var(--text-color);
}

.search {
    flex: 1;
    margin: 0 20px;
    padding: 8px 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

nav a {
    text-decoration: none;
    color: black;
    font-size: 14px;
}

.language-dropdown,
.discover-dropdown,
.user-dropdown {
    position: relative;
    display: inline-block;
}

.language-menu,
.discover-menu,
.user-menu {
    display: none;
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    list-style: none;
    padding: 5px;
    margin: 0;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    z-index: 1000;
}

.language-menu li,
.discover-menu li,
.user-menu li {
    padding: 5px 10px;
}

.language-menu li a,
.discover-menu li a,
.user-menu li a {
    text-decoration: none;
    color: black;
    display: block;
    white-space: nowrap;
}

.language-menu li a:hover,
.discover-menu li a:hover,
.user-menu li a:hover {
    background: #f0f0f0;
}

.language-dropdown:hover .language-menu,
.discover-dropdown:hover .discover-menu,
.user-dropdown:hover .user-menu {
    display: block;
}

.show-signup-btn,
.show-login-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
}

.show-signup-btn:hover,
.show-login-btn:hover {
    background: var(--text-color);
}

/* Login and Signup Modals */
.login-overlay,
.signup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.login,
.signup {
    display: none;
    z-index: 1001;
}

.login-container,
.signup-container {
    background-color: white;
    padding: 3rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    text-align: center;
}

.login-container h2,
.signup-container h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.form-group label {
    font-size: 1.1rem;
    color: #555;
    display: block;
    margin-bottom: 5px;
}

.form-group input,
.signup-container input,
.signup-container select {
    width: 100%;
    padding: 1rem;
    margin-top: 0.5rem;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
    transition: border-color 0.3s;
}

.form-group input:focus,
.signup-container input:focus,
.signup-container select:focus {
    border-color: var(--primary-color);
}

.login-btn,
.signup-container button {
    padding: 1rem 2rem;
    background-color: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    width: 100%;
    transition: background-color 0.3s;
}

.login-btn:hover,
.signup-container button:hover {
    background-color: var(--text-color);
}

.close-login-btn,
.close-signup-btn {
    padding: 0.9rem 1.3rem;
    background-color: #e6e6e6;
    color: rgb(0, 0, 0);
    font-size: 1rem;
    font-weight: bold;
    border: none;
    border-radius: 100px;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s;
    position: absolute;
    top: 10px;
    right: 10px;
}

.close-login-btn:hover,
.close-signup-btn:hover {
    background-color: #b4b4b4;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 120px 50px 50px;
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.hero h2 {
    font-size: 36px;
    margin-bottom: 15px;
}

.hero p {
    font-size: 18px;
    margin: 10px 0 20px;
}

.cta-btn {
    display: inline-block;
    padding: 12px 25px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.cta-btn:hover {
    transform: scale(1.05);
}

/* Services Section */
.services {
    padding: 80px 0 40px;
    background: var(--background-color);
}

.services h3 {
    text-align: center;
    font-size: 28px;
    margin-bottom: 40px;
    color: var(--primary-color);
}

.service-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: 0 50px;
}

.service-boxes {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    gap: 15px;
    padding: 10px;
    scrollbar-width: none;
}

.service-boxes::-webkit-scrollbar {
    display: none;
}

.service-box {
    flex: 0 0 auto;
    text-align: center;
    background: #f1f1f1;
    border-radius: 10px;
    transition: transform 0.3s ease-in-out;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    width: 180px;
}

.service-box img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.service-box:hover {
    transform: scale(1.05);
    background: #e6e6e6;
}

.scroll-btn {
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-color);
    border: none;
    cursor: pointer;
    padding: 15px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.scroll-btn.left {
    left: 10px;
}

.scroll-btn.right {
    right: 10px;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: var(--light-gray);
    text-align: center;
}

.testimonials h3 {
    font-size: 28px;
    margin-bottom: 40px;
    color: var(--primary-color);
}

.testimonial-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    padding: 0 50px;
}

.testimonial-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    flex: 1;
    min-width: 300px;
    max-width: 500px;
    text-align: left;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 15px;
    color: var(--text-color);
}

.testimonial-card span {
    font-weight: 600;
    color: var(--primary-color);
}

/* Stats Section */
.stats {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.stats h3 {
    font-size: 28px;
    margin-bottom: 40px;
    color: var(--primary-color);
}

.stat-boxes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    padding: 0 50px;
}

.stat-box {
    background: var(--light-gray);
    padding: 30px;
    border-radius: 10px;
    flex: 1;
    min-width: 200px;
    max-width: 300px;
}

.stat-box h4 {
    font-size: 36px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-box p {
    color: var(--text-color);
}

/* CTA Section */
.cta {
    padding: 100px 50px;
    background: var(--primary-color);
    color: white;
    text-align: center;
}

.cta h3 {
    font-size: 28px;
    margin-bottom: 20px;
}

.cta p {
    margin-bottom: 30px;
    font-size: 18px;
}

.cta .cta-btn {
    background: var(--accent-color);
    color: var(--text-color);
    font-weight: bold;
}

.cta .cta-btn:hover {
    transform: scale(1.05);
}

/* Why Us Section */
.why-us {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.why-us h3 {
    font-size: 28px;
    margin-bottom: 40px;
    color: var(--primary-color);
}

.reasons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    padding: 0 50px;
}

.reason-box {
    background: var(--light-gray);
    padding: 30px;
    border-radius: 10px;
    flex: 1;
    min-width: 250px;
    max-width: 350px;
}

.reason-box h4 {
    font-size: 20px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.reason-box p {
    color: var(--text-color);
}

/* Footer */
.footer {
    background: var(--light-gray);
    padding: 50px 0 20px;
    margin-top: 80px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer .logo {
    display: inline-block;
    margin-bottom: 30px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    text-decoration: none;
    color: var(--text-color);
    transition: color 0.3s;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.social-media {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.social-media a {
    color: var(--primary-color);
    font-size: 24px;
    transition: transform 0.3s;
}

.social-media a:hover {
    transform: scale(1.2);
}

.copyright {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-color);
}

.footer-bottom {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-bottom a {
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
}

.footer-bottom a:hover {
    text-decoration: underline;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    margin: 100px auto 50px;
    padding: 0 20px;
}

.dashboard-header {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.dashboard-header h2 {
    font-size: 28px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.dashboard-header p {
    color: var(--text-color);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card h3 {
    font-size: 36px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-card p {
    color: var(--text-color);
}

.dashboard-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.dashboard-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
    font-size: 20px;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.dashboard-card-content {
    max-height: 300px;
    overflow-y: auto;
}

/* Service Cards */
.services-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.service-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card-header {
    padding: 15px;
    background: var(--light-gray);
}

.service-card-header h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.service-card-body {
    padding: 15px;
}

.service-card-body p {
    margin-bottom: 10px;
    color: var(--text-color);
}

.service-card-footer {
    padding: 15px;
    background: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-price {
    font-weight: bold;
    color: var(--primary-color);
}

.service-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

/* Form Styles */
.form-container {
    max-width: 800px;
    margin: 100px auto 50px;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 16px;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Profile Styles */
.profile-container {
    max-width: 1000px;
    margin: 100px auto 50px;
    padding: 0 20px;
}

.profile-header {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 30px;
}

.profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    background-color: var(--light-gray);
}

.profile-info {
    flex: 1;
    min-width: 300px;
}

.profile-name {
    font-size: 28px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.profile-title {
    font-size: 18px;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.profile-details {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.profile-details h3 {
    font-size: 20px;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.profile-details p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.user-services {
    margin-top: 40px;
}

.user-services h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Search and Filter */
.filter-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.filter-container h3 {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--text-color);
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

/* ========================================
   RESPONSIVE DESIGN - COMPREHENSIVE
   ======================================== */

/* Mobile First Approach - Base styles for mobile */
@media (max-width: 480px) {
    /* Header - Mobile */
    .header {
        flex-direction: column;
        padding: 8px 15px;
        gap: 10px;
    }

    .logo {
        font-size: 20px;
        text-align: center;
    }

    .search {
        width: 100%;
        margin: 0;
        font-size: 14px;
        padding: 10px;
    }

    nav {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
        font-size: 12px;
    }

    nav a {
        padding: 5px 8px;
        font-size: 12px;
    }

    .show-signup-btn,
    .show-login-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    /* Hero Section - Mobile */
    .hero {
        padding: 120px 15px 40px;
        text-align: center;
    }

    .hero h2 {
        font-size: 24px;
        line-height: 1.3;
        margin-bottom: 15px;
    }

    .hero p {
        font-size: 16px;
        margin: 10px 0 25px;
    }

    .cta-btn {
        padding: 12px 20px;
        font-size: 14px;
        display: block;
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }

    /* Services Section - Mobile */
    .services {
        padding: 60px 0 30px;
    }

    .services h3 {
        font-size: 24px;
        margin-bottom: 30px;
        padding: 0 15px;
    }

    .service-container {
        padding: 0 15px;
    }

    .service-box {
        width: 150px;
        padding: 12px;
    }

    .service-box img {
        height: 100px;
    }

    .service-box span {
        font-size: 12px;
    }

    .scroll-btn {
        padding: 10px;
        font-size: 14px;
    }

    /* Testimonials - Mobile */
    .testimonials {
        padding: 60px 0;
    }

    .testimonials h3 {
        font-size: 24px;
        padding: 0 15px;
    }

    .testimonial-cards {
        padding: 0 15px;
        flex-direction: column;
    }

    .testimonial-card {
        min-width: auto;
        padding: 20px;
    }

    /* Stats Section - Mobile */
    .stats {
        padding: 60px 0;
    }

    .stats h3 {
        font-size: 24px;
        padding: 0 15px;
    }

    .stat-boxes {
        padding: 0 15px;
        flex-direction: column;
    }

    .stat-box {
        min-width: auto;
        padding: 20px;
    }

    .stat-box h4 {
        font-size: 28px;
    }

    /* Why Us Section - Mobile */
    .why-us {
        padding: 60px 0;
    }

    .why-us h3 {
        font-size: 24px;
        padding: 0 15px;
    }

    .reasons {
        padding: 0 15px;
        flex-direction: column;
    }

    .reason-box {
        min-width: auto;
        padding: 20px;
    }

    /* CTA Section - Mobile */
    .cta {
        padding: 60px 15px;
    }

    .cta h3 {
        font-size: 24px;
    }

    .cta p {
        font-size: 16px;
    }

    /* Footer - Mobile */
    .footer {
        padding: 40px 0 20px;
    }

    .footer-container {
        padding: 0 15px;
    }

    .footer-links {
        flex-direction: column;
        gap: 25px;
    }

    .footer-section {
        min-width: auto;
        text-align: center;
    }

    .social-media {
        gap: 15px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    /* Dashboard - Mobile */
    .dashboard-container {
        margin: 80px auto 30px;
        padding: 0 15px;
    }

    .dashboard-header {
        padding: 15px;
    }

    .dashboard-header h2 {
        font-size: 22px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .dashboard-card {
        padding: 15px;
    }

    /* Services List - Mobile */
    .services-list {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* Forms - Mobile */
    .form-container {
        margin: 80px auto 30px;
        padding: 20px 15px;
    }

    .form-container h2 {
        font-size: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        text-align: center;
    }

    /* Profile - Mobile */
    .profile-container {
        margin: 80px auto 30px;
        padding: 0 15px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-image {
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }

    .profile-info {
        min-width: auto;
    }

    .profile-name {
        font-size: 24px;
    }

    .profile-title {
        font-size: 16px;
    }

    .profile-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .profile-details {
        padding: 20px 15px;
    }

    /* Filter Container - Mobile */
    .filter-container {
        padding: 15px;
    }

    .filter-form {
        flex-direction: column;
        gap: 15px;
    }

    .filter-group {
        min-width: auto;
    }

    /* Modal Styles - Mobile */
    .login-container,
    .signup-container {
        padding: 2rem 1.5rem;
        margin: 20px;
        max-width: calc(100vw - 40px);
    }

    .login-container h2,
    .signup-container h2 {
        font-size: 1.5rem;
    }

    .close-login-btn,
    .close-signup-btn {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }
}

/* Tablet Portrait - 481px to 768px */
@media (min-width: 481px) and (max-width: 768px) {
    /* Header - Tablet */
    .header {
        flex-direction: row;
        flex-wrap: wrap;
        padding: 12px 20px;
        gap: 15px;
    }

    .logo {
        font-size: 22px;
    }

    .search {
        flex: 1;
        min-width: 200px;
        margin: 0 15px;
    }

    nav {
        gap: 12px;
        font-size: 13px;
    }

    /* Hero Section - Tablet */
    .hero {
        padding: 140px 30px 50px;
    }

    .hero h2 {
        font-size: 30px;
    }

    .hero p {
        font-size: 17px;
    }

    /* Services Section - Tablet */
    .service-container {
        padding: 0 30px;
    }

    .service-box {
        width: 160px;
    }

    /* Grid Layouts - Tablet */
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-list {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonial-cards {
        padding: 0 30px;
    }

    .stat-boxes,
    .reasons {
        padding: 0 30px;
    }

    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Profile - Tablet */
    .profile-header {
        flex-direction: row;
        align-items: center;
    }

    .profile-image {
        width: 180px;
        height: 180px;
    }
}

/* Tablet Landscape & Small Desktop - 769px to 1024px */
@media (min-width: 769px) and (max-width: 1024px) {
    /* Header - Small Desktop */
    .header {
        padding: 15px 25px;
    }

    .search {
        max-width: 300px;
    }

    /* Hero Section - Small Desktop */
    .hero {
        padding: 140px 40px 60px;
    }

    .hero h2 {
        font-size: 32px;
    }

    /* Services Section - Small Desktop */
    .service-container {
        padding: 0 40px;
    }

    .service-box {
        width: 170px;
    }

    /* Grid Layouts - Small Desktop */
    .dashboard-stats {
        grid-template-columns: repeat(3, 1fr);
    }

    .dashboard-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-list {
        grid-template-columns: repeat(3, 1fr);
    }

    .testimonial-cards,
    .stat-boxes,
    .reasons {
        padding: 0 40px;
    }

    .form-container {
        max-width: 700px;
    }
}

/* Large Desktop - 1025px to 1440px */
@media (min-width: 1025px) and (max-width: 1440px) {
    .header {
        padding: 15px 40px;
    }

    .hero {
        padding: 140px 60px 70px;
    }

    .service-container {
        padding: 0 60px;
    }

    .testimonial-cards,
    .stat-boxes,
    .reasons {
        padding: 0 60px;
    }

    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .services-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Extra Large Desktop - 1441px and up */
@media (min-width: 1441px) {
    .header {
        padding: 15px 60px;
    }

    .hero {
        padding: 140px 80px 80px;
    }

    .service-container {
        padding: 0 80px;
    }

    .testimonial-cards,
    .stat-boxes,
    .reasons {
        padding: 0 80px;
    }

    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .services-list {
        grid-template-columns: repeat(4, 1fr);
    }

    .form-container {
        max-width: 900px;
    }
}

/* ========================================
   RESPONSIVE UTILITIES & IMPROVEMENTS
   ======================================== */

/* Touch-friendly improvements for mobile devices */
@media (max-width: 768px) {
    /* Increase touch targets */
    .btn,
    .show-signup-btn,
    .show-login-btn,
    .cta-btn {
        min-height: 44px;
        padding: 12px 20px;
    }

    /* Improve form inputs for mobile */
    .form-group input,
    .form-group select,
    .form-group textarea,
    .search {
        min-height: 44px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Better spacing for mobile */
    .service-card {
        margin-bottom: 20px;
    }

    .dashboard-card {
        margin-bottom: 20px;
    }

    /* Improve dropdown menus for mobile */
    .language-menu,
    .discover-menu,
    .user-menu {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90vw;
        max-width: 300px;
        max-height: 70vh;
        overflow-y: auto;
        z-index: 9999;
    }

    .language-menu li,
    .discover-menu li,
    .user-menu li {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
    }

    .language-menu li:last-child,
    .discover-menu li:last-child,
    .user-menu li:last-child {
        border-bottom: none;
    }
}

/* Landscape orientation improvements */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 100px 20px 40px;
    }

    .hero h2 {
        font-size: 26px;
    }

    .header {
        padding: 8px 15px;
    }

    .dashboard-container {
        margin: 70px auto 20px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .service-box img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .cta,
    .btn,
    .show-signup-btn,
    .show-login-btn {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .dashboard-container,
    .form-container,
    .profile-container {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .service-box:hover,
    .service-card:hover,
    .cta-btn:hover {
        transform: none;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* This can be expanded later for dark mode support */
    .login-container,
    .signup-container {
        background-color: #1a1a1a;
        color: #ffffff;
    }
}

/* Container max-widths for different screen sizes */
.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
        padding: 0 20px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
        padding: 0 25px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
        padding: 0 30px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
        padding: 0 40px;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
        padding: 0 50px;
    }
}

/* ========================================
   DESKTOP RESPONSIVE FIXES
   ======================================== */

/* Ensure professional header works on all desktop sizes */
@media (min-width: 769px) {
    .professional-header {
        display: block !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
        background: #ffffff !important;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
    }

    .header-wrapper {
        width: 100% !important;
        display: block !important;
    }

    .header-top-bar {
        display: block !important;
        width: 100% !important;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%) !important;
    }

    .header-main {
        display: block !important;
        width: 100% !important;
        background: white !important;
    }

    .header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
        max-width: 1400px !important;
        margin: 0 auto !important;
        padding: 0 2rem !important;
    }

    .top-bar-content {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        width: 100% !important;
        max-width: 1400px !important;
        margin: 0 auto !important;
        padding: 0 2rem !important;
    }

    .logo-section {
        display: flex !important;
        flex-shrink: 0 !important;
    }

    .search-section {
        display: flex !important;
        flex: 1 !important;
        max-width: 600px !important;
        margin: 0 2rem !important;
    }

    .main-navigation {
        display: block !important;
        flex-shrink: 0 !important;
    }

    .nav-menu {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        list-style: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .nav-link {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        padding: 0.75rem 1rem !important;
        color: var(--text-color) !important;
        text-decoration: none !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
        white-space: nowrap !important;
    }

    .nav-link span {
        display: inline !important;
    }

    .user-info {
        display: flex !important;
        flex-direction: column !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    .mobile-nav-overlay {
        display: none !important;
    }

    body {
        padding-top: 60px !important;
    }
}

/* Large Desktop Optimizations */
@media (min-width: 1200px) {
    .header-content,
    .top-bar-content {
        padding: 0 3rem !important;
    }

    .search-section {
        max-width: 700px !important;
    }

    .nav-menu {
        gap: 1rem !important;
    }

    .nav-link {
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
    }
}

/* Extra Large Desktop */
@media (min-width: 1600px) {
    .header-content,
    .top-bar-content {
        max-width: 1600px !important;
        padding: 0 4rem !important;
    }

    .search-section {
        max-width: 800px !important;
    }
}

/* ========================================
   GLOBAL HEADER CONSISTENCY FIXES
   ======================================== */

/* Force professional header on all pages */
body {
    margin: 0;
    padding: 0;
}

/* Desktop header consistency */
@media (min-width: 1200px) {
    body {
        padding-top: 70px !important;
    }

    .professional-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    .professional-header .header-content,
    .professional-header .top-bar-content {
        max-width: 1600px !important;
        margin: 0 auto !important;
        padding: 0 3rem !important;
    }

    .professional-header .search-section {
        max-width: 500px !important;
    }

    .professional-header .nav-link {
        font-size: 0.9rem !important;
        padding: 0.6rem 1rem !important;
    }

    .professional-header .logo-main {
        font-size: 1.4rem !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    body {
        padding-top: 65px !important;
    }

    .professional-header .header-content,
    .professional-header .top-bar-content {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 2rem !important;
    }

    .professional-header .search-section {
        max-width: 400px !important;
    }
}

@media (min-width: 769px) and (max-width: 991px) {
    body {
        padding-top: 60px !important;
    }

    .professional-header .header-content {
        padding: 0 1.5rem !important;
    }

    .professional-header .search-section {
        max-width: 300px !important;
    }

    .professional-header .nav-link span {
        display: none !important;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 50px !important;
    }

    .professional-header .header-top-bar {
        display: none !important;
    }

    .professional-header .main-navigation {
        display: none !important;
    }

    .professional-header .search-section {
        display: none !important;
    }

    .professional-header .mobile-menu-toggle {
        display: flex !important;
    }
}

/* Ensure no conflicts with old header styles */
.header:not(.professional-header),
.navbar:not(.professional-header) {
    display: none !important;
}

/* Fix any layout issues */
.container,
.form-container,
.dashboard-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 1200px) {
    .container,
    .form-container,
    .dashboard-container {
        padding: 0 2rem;
    }
}

/* ========================================
   ADDITIONAL RESPONSIVE IMPROVEMENTS
   ======================================== */

/* Desktop Header Fixes */
@media (min-width: 993px) {
    .professional-header {
        display: block !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    body {
        padding-top: 60px !important;
    }

    .header-wrapper {
        width: 100% !important;
    }

    .header-top-bar,
    .header-main {
        width: 100% !important;
        display: block !important;
    }

    .header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

/* Improve header responsiveness */
@media (max-width: 992px) {
    .header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1000 !important;
        background: white !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }

    .professional-header .header-top-bar {
        display: none !important;
    }

    /* Adjust body padding for fixed header */
    body {
        padding-top: 70px;
    }
}

    /* Improve form inputs on mobile */
    input, select, textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
        -webkit-appearance: none;
        border-radius: 8px !important;
    }

    /* Better button sizing for touch */
    .btn, button, .cta-btn {
        min-height: 44px !important;
        padding: 12px 20px !important;
        font-size: 16px !important;
    }

    /* Improve modal sizing on mobile */
    .login-container,
    .signup-container {
        width: 95vw !important;
        max-width: 400px !important;
        margin: 10px !important;
        padding: 1.5rem !important;
    }

    /* Better spacing for mobile cards */
    .service-card,
    .dashboard-card,
    .testimonial-card {
        margin-bottom: 1rem !important;
    }

    /* Improve table responsiveness */
    table {
        font-size: 14px !important;
    }

    th, td {
        padding: 8px 4px !important;
    }

    /* Better image responsiveness */
    img {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Improve video responsiveness */
    video {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* Tablet specific improvements */
@media (min-width: 481px) and (max-width: 768px) {
    .header {
        padding: 12px 20px !important;
    }

    .hero {
        padding: 120px 25px 50px !important;
    }

    .hero h2 {
        font-size: 28px !important;
    }

    /* Better grid layouts for tablet */
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .services-list {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Large mobile landscape improvements */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 80px 20px 40px !important;
    }

    .hero h2 {
        font-size: 24px !important;
    }

    .modal-overlay {
        padding: 10px !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn, .cta-btn {
        border: 2px solid currentColor !important;
    }

    .service-card, .dashboard-card {
        border: 1px solid currentColor !important;
    }
}

/* Focus improvements for keyboard navigation */
a:focus, button:focus, input:focus, select:focus, textarea:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* Improve touch targets */
@media (pointer: coarse) {
    .btn, button, a, input, select {
        min-height: 44px !important;
        min-width: 44px !important;
    }
}

/* Print optimizations */
@media print {
    .header, .footer, .mobile-nav, .btn, button {
        display: none !important;
    }

    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: black !important;
        background: white !important;
    }

    .container, .dashboard-container, .form-container {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
    }
}
