<?php
// Authentication middleware
function requireLogin() {
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
        header("Location: login.php");
        exit;
    }
}

function requireFreelancer() {
    requireLogin();
    
    global $conn;
    $user_id = $_SESSION['user_id'];
    
    $stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user || $user['user_type'] !== 'freelancer') {
        $_SESSION['error'] = "Cette fonctionnalité est réservée aux freelancers.";
        header("Location: dashboard.php");
        exit;
    }
}

function requireClient() {
    requireLogin();
    
    global $conn;
    $user_id = $_SESSION['user_id'];
    
    $stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user || $user['user_type'] !== 'client') {
        $_SESSION['error'] = "Cette fonctionnalité est réservée aux clients.";
        header("Location: dashboard.php");
        exit;
    }
}

// Get current user details
function getCurrentUserDetails() {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    $user_id = $_SESSION['user_id'];
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

// Function to check if a user is pro
function isUserPro($userId) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT s.is_active 
        FROM subscriptions s 
        WHERE s.user_id = ? AND s.plan_type IN ('pro', 'premium') AND s.is_active = TRUE 
        AND (s.end_date IS NULL OR s.end_date > CURRENT_TIMESTAMP)
    ");
    
    $stmt->execute([$userId]);
    return $stmt->rowCount() > 0;
}
