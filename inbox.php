<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// جلب جميع الرسائل المستلمة
$stmt = $conn->prepare("SELECT m.*, u.full_name AS sender_name 
                        FROM messages m
                        JOIN users u ON m.sender_id = u.id
                        WHERE m.receiver_id = ?
                        ORDER BY m.sent_at DESC");
$stmt->execute([$user_id]);
$messages = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Boîte de réception</h1>

    <?php if (count($messages) === 0): ?>
        <p>Aucun message pour l’instant.</p>
    <?php else: ?>
        <div class="space-y-4">
            <?php foreach ($messages as $msg): ?>
                <div class="border p-4 rounded-md <?= $msg['is_read'] ? 'bg-white' : 'bg-blue-50' ?>">
                    <div class="flex justify-between">
                        <h3 class="font-semibold"><?= htmlspecialchars($msg['sender_name']) ?></h3>
                        <span class="text-sm text-gray-500"><?= date('d/m/Y H:i', strtotime($msg['sent_at'])) ?></span>
                    </div>
                    <p class="mt-2"><?= nl2br(htmlspecialchars_decode($msg['message'])) ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php
// تحديد كل الرسائل كـ "مقروءة"
$conn->prepare("UPDATE messages SET is_read = 1 WHERE receiver_id = ?")->execute([$user_id]);

include 'includes/footer.php';
?>
