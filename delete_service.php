<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Check if service ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID de service invalide.";
    header("Location: freelancer_dashboard.php");
    exit;
}

$service_id = intval($_GET['id']);

// Check if service exists and belongs to the user
$stmt = $conn->prepare("SELECT * FROM services WHERE id = ? AND user_id = ?");
$stmt->execute([$service_id, $user_id]);
$service = $stmt->fetch();

if (!$service) {
    $_SESSION['error'] = "Service introuvable ou vous n'avez pas les droits pour le supprimer.";
    header("Location: freelancer_dashboard.php");
    exit;
}

// Check if the service is being used in any active projects
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM projects
    WHERE service_id = ? AND status IN ('pending', 'in_progress')
");
$stmt->execute([$service_id]);
$active_projects = $stmt->fetch()['count'];

if ($active_projects > 0) {
    $_SESSION['error'] = "Ce service ne peut pas être supprimé car il est utilisé dans des projets actifs.";
    header("Location: freelancer_dashboard.php");
    exit;
}

// Check for confirmation via POST
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['confirm_delete'])) {
    try {
        $stmt = $conn->prepare("DELETE FROM services WHERE id = ? AND user_id = ?");
        $stmt->execute([$service_id, $user_id]);
        
        $_SESSION['success'] = "Le service a été supprimé avec succès.";
        header("Location: freelancer_dashboard.php");
        exit;
        
    } catch (PDOException $e) {
        $_SESSION['error'] = "Erreur lors de la suppression du service: " . $e->getMessage();
        header("Location: freelancer_dashboard.php");
        exit;
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2>Supprimer le Service</h2>
    
    <div class="alert alert-warning" style="background-color: #fff3cd; color: #856404; padding: 15px; margin-bottom: 20px; border-radius: 5px;">
        <h3 style="font-size: 1.2rem; margin-bottom: 10px;">Attention! Cette action est irréversible.</h3>
        <p>Vous êtes sur le point de supprimer le service suivant :</p>
        <p style="font-weight: bold; margin: 10px 0;"><?= htmlspecialchars($service['title']) ?></p>
        <p>Prix: <?= number_format($service['price'], 2) ?> DH</p>
    </div>
    
    <form method="POST" action="delete_service.php?id=<?= $service_id ?>">
        <div class="form-buttons">
            <a href="freelancer_dashboard.php" class="btn btn-secondary">Annuler</a>
            <button type="submit" name="confirm_delete" value="1" class="btn btn-danger">Confirmer la suppression</button>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
