<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Get search query
$q = isset($_GET['q']) ? sanitize($_GET['q']) : '';

// Don't search if query is empty
if (empty($q)) {
    header('Location: index.php');
    exit;
}

// Search services
$services = [];
if (!empty($q)) {
    $search_term = '%' . $q . '%';
    $stmt = $conn->prepare("
        SELECT s.*, u.username as freelancer_name, u.id as freelancer_id
        FROM services s
        JOIN users u ON s.user_id = u.id
        WHERE (s.title LIKE ? OR s.description LIKE ? OR s.category LIKE ?) AND s.status = 'active'
        ORDER BY s.created_at DESC
    ");
    $stmt->execute([$search_term, $search_term, $search_term]);
    $services = $stmt->fetchAll();
}

// Search freelancers
$freelancers = [];
if (!empty($q)) {
    $search_term = '%' . $q . '%';
    $stmt = $conn->prepare("
        SELECT u.*, 
               (SELECT AVG(r.rating) FROM reviews r WHERE r.reviewee_id = u.id) as avg_rating,
               (SELECT COUNT(*) FROM projects p WHERE p.freelancer_id = u.id AND p.status = 'completed') as completed_projects,
               (SELECT COUNT(*) FROM services s WHERE s.user_id = u.id AND s.status = 'active') as active_services
        FROM users u
        WHERE (u.username LIKE ? OR u.full_name LIKE ? OR u.bio LIKE ?) AND u.user_type = 'freelancer'
        ORDER BY avg_rating DESC, completed_projects DESC
    ");
    $stmt->execute([$search_term, $search_term, $search_term]);
    $freelancers = $stmt->fetchAll();
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8 mt-20">
    <h1 class="text-3xl font-bold mb-6">Résultats de recherche pour "<?= htmlspecialchars($q) ?>"</h1>
    
    <!-- Search Statistics -->
    <div class="mb-8">
        <p><?= count($services) ?> service(s) et <?= count($freelancers) ?> freelancer(s) trouvé(s)</p>
    </div>
    
    <!-- Freelancers Section -->
    <?php if (!empty($freelancers)): ?>
        <h2 class="text-2xl font-bold mb-4">Freelancers</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
            <?php foreach ($freelancers as $freelancer): ?>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center text-lg font-bold text-gray-700 mr-4">
                            <?= strtoupper(substr($freelancer['username'], 0, 1)) ?>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg"><?= htmlspecialchars($freelancer['full_name']) ?></h3>
                            <p class="text-gray-600">@<?= htmlspecialchars($freelancer['username']) ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($freelancer['bio'])): ?>
                        <p class="text-gray-700 mb-4">
                            <?= htmlspecialchars(substr($freelancer['bio'], 0, 100) . (strlen($freelancer['bio']) > 100 ? '...' : '')) ?>
                        </p>
                    <?php else: ?>
                        <p class="text-gray-500 italic mb-4">Aucune biographie disponible</p>
                    <?php endif; ?>
                    
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <div class="text-yellow-400 mr-1">
                                <?php 
                                $avg_rating = $freelancer['avg_rating'] ? round($freelancer['avg_rating']) : 0;
                                for ($i = 1; $i <= 5; $i++) {
                                    echo $i <= $avg_rating ? '★' : '☆';
                                }
                                ?>
                            </div>
                            <span class="text-sm"><?= number_format($freelancer['avg_rating'] ?? 0, 1) ?>/5</span>
                        </div>
                        <span class="text-sm text-gray-600"><?= $freelancer['completed_projects'] ?? 0 ?> projets</span>
                    </div>
                    
                    <div class="border-t pt-4 mt-2">
                        <a href="profile.php?id=<?= $freelancer['id'] ?>" class="btn btn-primary w-full text-center">Voir Profil</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Services Section -->
    <?php if (!empty($services)): ?>
        <h2 class="text-2xl font-bold mb-4">Services</h2>
        
        <div class="services-list">
            <?php foreach ($services as $service): ?>
                <div class="service-card">
                    <div class="service-card-header">
                        <h3><?= htmlspecialchars($service['title']) ?></h3>
                        <p class="text-sm text-gray-500">
                            Par: <a href="profile.php?id=<?= $service['freelancer_id'] ?>" class="text-blue-600 hover:underline">
                                <?= htmlspecialchars($service['freelancer_name']) ?>
                            </a>
                        </p>
                    </div>
                    
                    <div class="service-card-body">
                        <p><?= nl2br(htmlspecialchars(substr($service['description'], 0, 120) . (strlen($service['description']) > 120 ? '...' : ''))) ?></p>
                        <span class="mt-2 inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                            <?= htmlspecialchars($service['category']) ?>
                        </span>
                    </div>
                    
                    <div class="service-card-footer">
                        <span class="service-price"><?= number_format($service['price'], 2) ?> DH</span>
                        <a href="view_service.php?id=<?= $service['id'] ?>" class="btn btn-primary">Voir détails</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- No Results -->
    <?php if (empty($services) && empty($freelancers)): ?>
        <div class="bg-gray-50 p-8 rounded-lg text-center">
            <p class="text-lg mb-4">Aucun résultat trouvé pour "<?= htmlspecialchars($q) ?>".</p>
            <p class="mb-4">Essayez d'autres mots-clés ou parcourez toutes les catégories.</p>
            <a href="browse_services.php" class="btn btn-primary">Parcourir tous les services</a>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
