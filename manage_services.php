<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Vérifier si l'utilisateur est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

include 'includes/admin_header.php'; // Inclure l'entête de l'admin

// Récupérer tous les services non approuvés
$stmt = $conn->query("SELECT * FROM services WHERE approved = 0 ORDER BY id DESC");
$services = $stmt->fetchAll();
?>

<h2 class="text-2xl font-bold mb-6">Services non approuvés</h2>

<!-- Tableau des services non approuvés -->
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table class="min-w-full text-sm text-left">
        <thead class="bg-gray-100 text-gray-700 uppercase">
            <tr>
                <th class="px-6 py-3">#</th>
                <th class="px-6 py-3">Nom du service</th>
                <th class="px-6 py-3">Description</th>
                <th class="px-6 py-3">Statut</th>
                <th class="px-6 py-3 text-right">Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($services as $index => $service): ?>
                <tr class="border-t hover:bg-gray-50">
                    <td class="px-6 py-4"><?= $index + 1 ?></td>
                    <td class="px-6 py-4"><?= htmlspecialchars($service['service_name']) ?></td>
                    <td class="px-6 py-4"><?= htmlspecialchars($service['description']) ?></td>
                    <td class="px-6 py-4"><?= $service['approved'] ? 'Approuvé' : 'En attente d\'approbation' ?></td>
                    <td class="px-6 py-4 text-right">
                        <!-- Formulaire pour approuver le service -->
                        <form method="POST" action="approve_service.php">
                            <input type="hidden" name="service_id" value="<?= $service['id'] ?>">
                            <button type="submit" class="text-green-600 hover:underline">Approuver</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<?php include 'includes/admin_footer.php'; // Inclure le pied de page ?>
