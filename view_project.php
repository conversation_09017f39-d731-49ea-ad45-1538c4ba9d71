<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

// Check if project ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID de projet invalide.";
    header("Location: dashboard.php");
    exit;
}

$project_id = intval($_GET['id']);
$user_id = $_SESSION['user_id'];

// Get project data
$stmt = $conn->prepare("
    SELECT p.*, 
           c.username as client_username, c.full_name as client_name, c.email as client_email,
           f.username as freelancer_username, f.full_name as freelancer_name, f.email as freelancer_email,
           s.title as service_title, s.category as service_category
    FROM projects p
    JOIN users c ON p.client_id = c.id
    JOIN users f ON p.freelancer_id = f.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.id = ? AND (p.client_id = ? OR p.freelancer_id = ?)
");
$stmt->execute([$project_id, $user_id, $user_id]);
$project = $stmt->fetch();

if (!$project) {
    $_SESSION['error'] = "Projet introuvable ou vous n'avez pas les droits pour y accéder.";
    header("Location: dashboard.php");
    exit;
}

// Determine if the logged-in user is the client or the freelancer
$is_client = ($user_id == $project['client_id']);
$is_freelancer = ($user_id == $project['freelancer_id']);

// Check if the user has already left a review for this project
$has_reviewed = false;
if ($project['status'] === 'completed') {
    $stmt = $conn->prepare("
        SELECT * FROM reviews 
        WHERE project_id = ? AND reviewer_id = ?
    ");
    $stmt->execute([$project_id, $user_id]);
    $has_reviewed = ($stmt->rowCount() > 0);
}

// Process form submission
$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Handle project status update
    if (isset($_POST['update_status']) && $is_freelancer) {
        $new_status = sanitize($_POST['status']);
        
        if ($new_status === 'completed' && $project['status'] !== 'completed') {
            // Set completion date when marking as completed
            $stmt = $conn->prepare("
                UPDATE projects 
                SET status = ?, completion_date = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $project_id]);
        } else {
            $stmt = $conn->prepare("
                UPDATE projects 
                SET status = ? 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $project_id]);
        }
        
        $success = "Statut du projet mis à jour avec succès.";
        
        // Refresh project data
        $stmt = $conn->prepare("
    SELECT p.*, 
           c.username as client_username, c.full_name as client_name, c.email as client_email,
           f.username as freelancer_username, f.full_name as freelancer_name, f.email as freelancer_email,
           s.title as service_title, s.category as service_category
    FROM projects p
    JOIN users c ON p.client_id = c.id
    JOIN users f ON p.freelancer_id = f.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.id = ? AND (p.client_id = ? OR p.freelancer_id = ?)
");
$stmt->execute([$project_id, $user_id, $user_id]);
$project = $stmt->fetch();

    }
    
    // Handle review submission
    if (isset($_POST['submit_review']) && $project['status'] === 'completed' && !$has_reviewed) {
        $rating = intval($_POST['rating']);
        $comment = sanitize($_POST['comment']);
        
        if ($rating < 1 || $rating > 5) {
            $error = "Veuillez donner une note entre 1 et 5.";
        } elseif (empty($comment)) {
            $error = "Veuillez laisser un commentaire.";
        } else {
            // Determine the reviewee
            $reviewee_id = $is_client ? $project['freelancer_id'] : $project['client_id'];
            
            $stmt = $conn->prepare("
                INSERT INTO reviews (project_id, reviewer_id, reviewee_id, rating, comment)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$project_id, $user_id, $reviewee_id, $rating, $comment]);
            
            $success = "Votre avis a été soumis avec succès.";
            $has_reviewed = true;
        }
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8 mt-20">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Project Header -->
        <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2"><?= htmlspecialchars($project['title']) ?></h1>
                    
                    <div class="flex flex-wrap items-center text-sm text-gray-600 mb-2">
                        <?php if (!empty($project['service_title'])): ?>
                            <span class="mr-4">
                                <i class="ri-shopping-bag-line mr-1"></i> <?= htmlspecialchars($project['service_title']) ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($project['service_category'])): ?>
                            <span class="mr-4">
                                <i class="ri-folder-line mr-1"></i> <?= htmlspecialchars($project['service_category']) ?>
                            </span>
                        <?php endif; ?>
                        
                        <span>
                            <i class="ri-time-line mr-1"></i> Créé le <?= date('d/m/Y', strtotime($project['created_at'])) ?>
                        </span>
                    </div>
                </div>
                
                <div>
                    <span class="px-3 py-1 rounded-full text-sm font-semibold 
                          <?php 
                          switch ($project['status']) {
                              case 'pending':
                                  echo 'bg-yellow-100 text-yellow-800';
                                  break;
                              case 'in_progress':
                                  echo 'bg-blue-100 text-blue-800';
                                  break;
                              case 'completed':
                                  echo 'bg-green-100 text-green-800';
                                  break;
                              case 'cancelled':
                                  echo 'bg-red-100 text-red-800';
                                  break;
                          }
                          ?>">
                        <?php 
                        switch ($project['status']) {
                            case 'pending':
                                echo 'En attente';
                                break;
                            case 'in_progress':
                                echo 'En cours';
                                break;
                            case 'completed':
                                echo 'Terminé';
                                break;
                            case 'cancelled':
                                echo 'Annulé';
                                break;
                        }
                        ?>
                    </span>
                </div>
            </div>
            
            <!-- Project details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <h3 class="font-semibold mb-2">Client</h3>
                    <p>
                        <a href="profile.php?id=<?= $project['client_id'] ?>" class="text-blue-600 hover:underline">
                            <?= htmlspecialchars($project['client_name']) ?>
                        </a>
                    </p>
                    <?php if ($is_freelancer || $is_client): ?>
                        <p class="text-gray-600"><?= htmlspecialchars($project['client_email']) ?></p>
                    <?php endif; ?>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-2">Freelancer</h3>
                    <p>
                        <a href="profile.php?id=<?= $project['freelancer_id'] ?>" class="text-blue-600 hover:underline">
                            <?= htmlspecialchars($project['freelancer_name']) ?>
                        </a>
                    </p>
                    <?php if ($is_freelancer || $is_client): ?>
                        <p class="text-gray-600"><?= htmlspecialchars($project['freelancer_email']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Project Content -->
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Left Column - Project Details -->
                <div class="md:col-span-2">
                    <h2 class="text-xl font-bold mb-4">Description du projet</h2>
                    <div class="prose max-w-none">
                        <?= nl2br(htmlspecialchars($project['description'])) ?>
                    </div>
                    
                    <!-- Project timeline -->
                    <div class="mt-10">
                        <h3 class="text-lg font-semibold mb-4">Chronologie du projet</h3>
                        
                        <div class="space-y-4">
                            <div class="flex">
                                <div class="flex-none w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="ri-calendar-check-line text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">Projet créé</p>
                                    <p class="text-gray-600 text-sm"><?= date('d/m/Y', strtotime($project['created_at'])) ?></p>
                                </div>
                            </div>
                            
                            <?php if ($project['status'] !== 'pending'): ?>
                            <div class="flex">
                                <div class="flex-none w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="ri-time-line text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">Projet commencé</p>
                                    <p class="text-gray-600 text-sm">
                                        <?= isset($project['start_date']) && !empty($project['start_date']) ? date('d/m/Y', strtotime($project['start_date'])) : 'تاريخ غير متوفر' ?>
                                    </p>

                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project['status'] === 'completed'): ?>
                            <div class="flex">
                                <div class="flex-none w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="ri-check-line text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">Projet terminé</p>
                                    <p class="text-gray-600 text-sm"><?= date('d/m/Y', strtotime($project['completion_date'])) ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project['status'] === 'cancelled'): ?>
                            <div class="flex">
                                <div class="flex-none w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="ri-close-line text-red-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">Projet annulé</p>
                                    <p class="text-gray-600 text-sm"><?= date('d/m/Y', strtotime($project['updated_at'])) ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Status update form for freelancers -->
                    <?php if ($is_freelancer && $project['status'] !== 'completed' && $project['status'] !== 'cancelled'): ?>
                    <div class="mt-10 border-t pt-6">
                        <h3 class="text-lg font-semibold mb-4">Mettre à jour le statut</h3>
                        
                        <?php if (!empty($error)): ?>
                            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                                <p><?= $error ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                                <p><?= $success ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="view_project.php?id=<?= $project_id ?>">
                            <div class="mb-4">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Nouveau statut</label>
                                <select id="status" name="status" class="w-full px-3 py-2 border rounded-md" required>
                                    <?php if ($project['status'] === 'pending'): ?>
                                        <option value="pending" selected>En attente</option>
                                        <option value="in_progress">En cours</option>
                                        <option value="cancelled">Annulé</option>
                                    <?php elseif ($project['status'] === 'in_progress'): ?>
                                        <option value="in_progress" selected>En cours</option>
                                        <option value="completed">Terminé</option>
                                        <option value="cancelled">Annulé</option>
                                    <?php else: ?>
                                        <option value="pending">En attente</option>
                                        <option value="in_progress">En cours</option>
                                        <option value="completed">Terminé</option>
                                        <option value="cancelled">Annulé</option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <button type="submit" name="update_status" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition">
                                Mettre à jour le statut
                            </button>
                        </form>


                    </div>
                    <?php endif; ?>
                    
                    <!-- Review form -->
                    <?php if ($project['status'] === 'completed' && !$has_reviewed): ?>
                    <div class="mt-10 border-t pt-6">
                        <h3 class="text-lg font-semibold mb-4">Laisser un avis</h3>
                        
                        <?php if (!empty($error)): ?>
                            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                                <p><?= $error ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                                <p><?= $success ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="view_project.php?id=<?= $project_id ?>">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Note</label>
                                <div class="flex space-x-2">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <div>
                                        <input type="radio" id="rating-<?= $i ?>" name="rating" value="<?= $i ?>" class="hidden" required>
                                        <label for="rating-<?= $i ?>" class="block w-10 h-10 bg-gray-200 rounded-full cursor-pointer flex items-center justify-center hover:bg-yellow-200 transition">
                                            <?= $i ?>
                                        </label>
                                    </div>
                                    <?php endfor; ?>
                                </div>
                                <script>
                                    // Highlight selected rating
                                    document.querySelectorAll('input[name="rating"]').forEach(radio => {
                                        radio.addEventListener('change', function() {
                                            // Reset all labels
                                            document.querySelectorAll('label[for^="rating-"]').forEach(label => {
                                                label.classList.remove('bg-yellow-400');
                                                label.classList.add('bg-gray-200');
                                            });
                                            
                                            // Highlight selected and lower ratings
                                            const value = parseInt(this.value);
                                            for (let i = 1; i <= value; i++) {
                                                const label = document.querySelector(`label[for="rating-${i}"]`);
                                                label.classList.remove('bg-gray-200');
                                                label.classList.add('bg-yellow-400');
                                            }
                                        });
                                    });
                                </script>
                            </div>
                            
                            <div class="mb-4">
                                <label for="comment" class="block text-sm font-medium text-gray-700 mb-1">Commentaire</label>
                                <textarea id="comment" name="comment" rows="4" class="w-full px-3 py-2 border rounded-md" required></textarea>
                            </div>
                            
                            <button type="submit" name="submit_review" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition">
                                Soumettre l'avis
                            </button>
                        </form>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Right Column - Project Info -->
                <div>
                    <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Informations</h3>
                        
                        <ul class="space-y-4">
                            <li class="flex justify-between">
                                <span class="text-gray-600">Prix</span>
                                <span class="font-medium"><?= number_format($project['price'], 2) ?> DH</span>
                            </li>
                            
                            <li class="flex justify-between">
                                <span class="text-gray-600">Statut</span>
                                <span class="font-medium">
                                    <?php 
                                    switch ($project['status']) {
                                        case 'pending':
                                            echo 'En attente';
                                            break;
                                        case 'in_progress':
                                            echo 'En cours';
                                            break;
                                        case 'completed':
                                            echo 'Terminé';
                                            break;
                                        case 'cancelled':
                                            echo 'Annulé';
                                            break;
                                    }
                                    ?>
                                </span>
                            </li>
                            
                            <li class="flex justify-between">
                                <span class="text-gray-600">Date de création</span>
                                <span class="font-medium"><?= date('d/m/Y', strtotime($project['created_at'])) ?></span>
                            </li>
                            
                            <?php if ($project['status'] === 'completed' && !empty($project['completion_date'])): ?>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Date de fin</span>
                                <span class="font-medium"><?= date('d/m/Y', strtotime($project['completion_date'])) ?></span>
                            </li>
                            <?php endif; ?>
                            
                            <?php if (!empty($project['deadline'])): ?>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Date limite</span>
                                <span class="font-medium"><?= date('d/m/Y', strtotime($project['deadline'])) ?></span>
                            </li>
                            <?php endif; ?>
                        </ul>
                        
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <a href="<?= $is_client ? 'client_dashboard.php' : 'freelancer_dashboard.php' ?>" class="block text-center bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md transition">
                                Retour au Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
