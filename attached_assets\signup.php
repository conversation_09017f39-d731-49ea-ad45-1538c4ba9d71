<?php
session_start();
require 'db.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $username = $_POST['username'] ?? '';
    $full_name = $_POST['full_name'] ?? '';
    $user_type = $_POST['user_type'] ?? ''; // client أو freelancer

    if (!empty($email) && !empty($password) && !empty($confirm_password) && !empty($username) && !empty($full_name) && !empty($user_type)) {

        if ($password !== $confirm_password) {
            $error = "Les mots de passe ne correspondent pas.";
        } else {
            $password_hashed = password_hash($password, PASSWORD_DEFAULT);

            $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->rowCount() > 0) {
                $error = "Cet email est déjà utilisé.";
            } else {
                $stmt = $conn->prepare("INSERT INTO users (username, email, password, user_type, full_name) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$username, $email, $password_hashed, $user_type, $full_name]);

                $_SESSION['user_id'] = $conn->lastInsertId();
                $_SESSION['email'] = $email;
                header("Location: dashboard.php");
                exit;
            }
        }
    } else {
        $error = "Tous les champs sont requis.";
    }
}

if (isset($error)) {
    echo "<p style='color:red;'>$error</p>";
}
?>