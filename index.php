<?php
include 'db.php';
$conn = $pdo;
include 'includes/header.php';
?>

<!-- Modern Hero Section -->
<section class="hero-modern">
    <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
    </div>

    <div class="container">
        <div class="hero-content-modern">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="ri-star-fill"></i>
                    <span>Plateforme #1 au Maroc</span>
                </div>

                <h1 class="hero-title-modern">
                    Connectez-vous avec les
                    <span class="text-gradient">meilleurs freelances</span>
                    marocains
                </h1>

                <p class="hero-description-modern">
                    Découvrez des talents exceptionnels dans le développement web, marketing digital,
                    design graphique et bien plus. Réalisez vos projets avec des professionnels qualifiés.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Freelances</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Projets réalisés</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.9/5</span>
                        <span class="stat-label">Satisfaction</span>
                    </div>
                </div>

                <div class="hero-actions-modern">
                    <a href="signup.php?type=freelancer" class="btn btn-primary btn-lg">
                        <i class="ri-user-star-line"></i>
                        <span>Devenir freelance</span>
                    </a>
                    <a href="browse_services.php" class="btn btn-outline btn-lg">
                        <i class="ri-search-line"></i>
                        <span>Trouver un service</span>
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="hero-video-modern">
                    <video src="IMG/SawbLi.mp4" autoplay loop muted playsinline>
                        <source src="IMG/SawbLi.mp4" type="video/mp4">
                    </video>
                    <div class="video-overlay"></div>
                </div>

                <div class="floating-cards">
                    <div class="floating-card card-1">
                        <i class="ri-code-line"></i>
                        <span>Développement</span>
                    </div>
                    <div class="floating-card card-2">
                        <i class="ri-palette-line"></i>
                        <span>Design</span>
                    </div>
                    <div class="floating-card card-3">
                        <i class="ri-megaphone-line"></i>
                        <span>Marketing</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* ========================================
   MODERN HOMEPAGE STYLES
   ======================================== */

/* Modern Hero Section */
.hero-modern {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: var(--space-20) 0;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        var(--primary-50) 0%,
        var(--primary-100) 25%,
        var(--gray-50) 50%,
        var(--primary-50) 75%,
        var(--primary-100) 100%);
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, var(--primary-200) 0%, transparent 50%),
                      radial-gradient(circle at 75% 75%, var(--primary-100) 0%, transparent 50%);
    opacity: 0.3;
}

.hero-content-modern {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
    position: relative;
    z-index: 1;
}

@media (min-width: 1024px) {
    .hero-content-modern {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-16);
    }
}

.hero-text {
    text-align: center;
}

@media (min-width: 1024px) {
    .hero-text {
        text-align: left;
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-100);
    color: var(--primary-700);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-6);
    animation: fadeInUp 0.6s ease-out;
}

.hero-title-modern {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

@media (min-width: 768px) {
    .hero-title-modern {
        font-size: var(--text-5xl);
    }
}

@media (min-width: 1024px) {
    .hero-title-modern {
        font-size: var(--text-6xl);
    }
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description-modern {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    max-width: 600px;
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

@media (min-width: 1024px) {
    .hero-description-modern {
        font-size: var(--text-xl);
        max-width: none;
    }
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
    justify-content: center;
    margin-bottom: var(--space-8);
    animation: fadeInUp 0.6s ease-out 0.6s both;
}

@media (min-width: 1024px) {
    .hero-stats {
        justify-content: flex-start;
    }
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin-top: var(--space-1);
}

.hero-actions-modern {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 0.6s ease-out 0.8s both;
}

@media (min-width: 1024px) {
    .hero-actions-modern {
        justify-content: flex-start;
    }
}

/* Hero Visual Section */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-video-modern {
    position: relative;
    width: 100%;
    max-width: 600px;
    border-radius: var(--radius-3xl);
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
    animation: fadeInUp 0.8s ease-out 1s both;
}

.hero-video-modern video {
    width: 100%;
    height: auto;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        var(--primary-600) 0%,
        transparent 30%,
        transparent 70%,
        var(--primary-600) 100%);
    opacity: 0.1;
    pointer-events: none;
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: var(--text-lg);
    color: var(--primary-600);
}

.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 20%;
    left: -15%;
    animation-delay: 1s;
}

.card-3 {
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-modern {
        min-height: 80vh;
        padding: var(--space-16) 0;
    }

    .hero-title-modern {
        font-size: var(--text-3xl);
    }

    .hero-description-modern {
        font-size: var(--text-base);
    }

    .hero-stats {
        gap: var(--space-4);
    }

    .hero-actions-modern {
        flex-direction: column;
        align-items: stretch;
    }

    .floating-cards {
        display: none;
    }
}









/* ========================================
   HEADER CONSISTENCY OVERRIDES
   ======================================== */

/* Ensure header works properly on homepage */
@media (min-width: 769px) {
    body {
        padding-top: 70px !important;
    }

    .professional-header {
        position: fixed !important;
        top: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 50px !important;
    }

    .header-top-bar {
        display: none !important;
    }

    .main-navigation {
        display: none !important;
    }

    .search-section {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }
}

/* Animation for smooth loading */
.hero-video-container,
.hero-section {
    animation: fadeInUp 0.8s ease-out;
}

/* Modern Services Section */
.services-modern {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.section-header {
    margin-bottom: var(--space-16);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-100);
    color: var(--primary-700);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-4);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

@media (min-width: 768px) {
    .section-title {
        font-size: var(--text-4xl);
    }
}

.section-description {
    font-size: var(--text-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-16);
}

.service-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-decoration: none;
    color: inherit;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.service-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
    font-size: var(--text-2xl);
}

.service-icon-primary {
    background: var(--primary-100);
    color: var(--primary-600);
}

.service-icon-success {
    background: var(--success-100);
    color: var(--success-600);
}

.service-icon-warning {
    background: var(--warning-100);
    color: var(--warning-600);
}

.service-icon-error {
    background: var(--error-100);
    color: var(--error-600);
}

.service-icon-info {
    background: var(--info-100);
    color: var(--info-600);
}

.service-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.service-description {
    font-size: var(--text-base);
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
}

.service-arrow {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: var(--primary-600);
    font-size: var(--text-lg);
    transition: transform var(--transition-fast);
}

.service-card:hover .service-arrow {
    transform: translateX(4px);
}

.services-cta {
    padding: var(--space-8);
    background: white;
    border-radius: var(--radius-2xl);
    border: 2px dashed var(--gray-300);
}

.cta-text {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Modern Services Section -->
<section class="services-modern">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="ri-service-line"></i>
                <span>Nos Services</span>
            </div>
            <h2 class="section-title">
                Explorez nos <span class="text-gradient">catégories de services</span>
            </h2>
            <p class="section-description">
                Découvrez une large gamme de services professionnels proposés par nos freelances experts
            </p>
        </div>

        <div class="services-grid">
            <?php
            // Fetch categories from database
            $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
            $categories = $stmt->fetchAll();

            $icons = [
                'Développement' => 'ri-code-line',
                'Marketing Digital' => 'ri-megaphone-line',
                'Graphisme' => 'ri-palette-line',
                'Montage Vidéo' => 'ri-movie-line',
                'Analyse de Données' => 'ri-bar-chart-line',
                'Musique & Voix Off' => 'ri-music-line',
                'Rédaction de Scripts' => 'ri-quill-pen-line',
                'Traduction' => 'ri-translate-line',
                'Cours en Ligne' => 'ri-graduation-cap-line'
            ];

            $colors = [
                'Développement' => 'primary',
                'Marketing Digital' => 'success',
                'Graphisme' => 'warning',
                'Montage Vidéo' => 'error',
                'Analyse de Données' => 'info',
                'Musique & Voix Off' => 'primary',
                'Rédaction de Scripts' => 'success',
                'Traduction' => 'warning',
                'Cours en Ligne' => 'error'
            ];

            foreach ($categories as $category) {
                $icon = $icons[$category['name']] ?? 'ri-service-line';
                $color = $colors[$category['name']] ?? 'primary';

                echo '<a href="browse_services.php?category=' . urlencode($category['name']) . '" class="service-card">';
                echo '<div class="service-icon service-icon-' . $color . '">';
                echo '<i class="' . $icon . '"></i>';
                echo '</div>';
                echo '<h3 class="service-title">' . htmlspecialchars($category['name']) . '</h3>';
                echo '<p class="service-description">Découvrez nos experts en ' . strtolower(htmlspecialchars($category['name'])) . '</p>';
                echo '<div class="service-arrow">';
                echo '<i class="ri-arrow-right-line"></i>';
                echo '</div>';
                echo '</a>';
            }
            ?>
        </div>

        <div class="services-cta text-center">
            <p class="cta-text">Vous ne trouvez pas ce que vous cherchez ?</p>
            <a href="browse_services.php" class="btn btn-outline btn-lg">
                <i class="ri-search-line"></i>
                <span>Voir tous les services</span>
            </a>
        </div>
    </div>
</section>


<!-- Reviews Section -->
<section id="testimonials" class="testimonials">
    <h3>Avis de nos clients</h3>
    <div class="testimonial-cards">
        <?php
        // Get a few random reviews
        $stmt = $conn->query("
            SELECT r.rating, r.comment, u.full_name, u.user_type 
            FROM reviews r
            JOIN users u ON r.reviewer_id = u.id
            ORDER BY RAND()
            LIMIT 3
        ");
        
        $reviews = $stmt->fetchAll();
        
        if (count($reviews) > 0) {
            foreach ($reviews as $review) {
                echo '<div class="testimonial-card">';
                echo '<p>"' . htmlspecialchars_decode($review['comment']) . '"</p>';
                echo '<span>- ' . htmlspecialchars_decode($review['full_name']) . ', ' . 
                     ($review['user_type'] == 'client' ? 'client' : 'freelancer') . '</span>';
                echo '</div>';
            }
        } else {
            // Default testimonials if no reviews in database
            ?>
            <div class="testimonial-card">
                <p>"SawbLi a transformé ma carrière de freelance ! Les projets sont variés et les clients très
                    satisfaits."</p>
                <span>- Brahim, programmeur</span>
            </div>
            <div class="testimonial-card">
                <p>"Une plateforme simple et efficace pour trouver des freelances de qualité en marketing digital."</p>
                <span>- Lamyae, société de marketing</span>
            </div>
            <?php
        }
        ?>
    </div>
</section>

<!-- Stats Section -->
<section id="stats" class="stats">
    <h3>Statistiques SawbLi</h3>
    <div class="stat-boxes">
        <?php
        // Get actual stats from database
        $projectCount = $conn->query("SELECT COUNT(*) as count FROM projects")->fetch()['count'] ?? 0;
        $freelancerCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'freelancer'")->fetch()['count'] ?? 0;
        $clientCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")->fetch()['count'] ?? 0;
        
        // If stats are too low, use default values
        $projectCount = max($projectCount, 1200);
        $freelancerCount = max($freelancerCount, 300);
        $clientCount = max($clientCount, 500);
        ?>
        <div class="stat-box">
            <h4><?= $projectCount ?>+</h4>
            <p>Projets réalisés</p>
        </div>
        <div class="stat-box">
            <h4><?= $freelancerCount ?>+</h4>
            <p>Indépendants inscrits</p>
        </div>
        <div class="stat-box">
            <h4><?= $clientCount ?>+</h4>
            <p>Clients satisfaits</p>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="cta" class="cta">
    <h3>Êtes-vous prêt à devenir freelance ou à embaucher les meilleurs ?</h3>
    <p>Rejoignez SawbLi et commencez votre aventure maintenant !</p>
    <a href="signup.php" class="cta-btn">Rejoignez-nous</a>
</section>

<!-- Why Choose Us Section -->
<section id="why-us" class="why-us">
    <h3>Pourquoi choisir SawbLi ?</h3>
    <div class="reasons">
        <div class="reason-box">
            <h4>Facilité d'utilisation</h4>
            <p>Notre interface est simple et conviviale.</p>
        </div>
        <div class="reason-box">
            <h4>Assistance continue</h4>
            <p>Assistance technique 24h/24 et 7j/7 disponible pour tous les membres.</p>
        </div>
        <div class="reason-box">
            <h4>Haute fiabilité</h4>
            <p>Systèmes de sécurité pour assurer des transactions sécurisées.</p>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
