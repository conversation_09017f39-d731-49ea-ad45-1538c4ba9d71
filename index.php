<?php
include 'db.php';
$conn = $pdo;
include 'includes/header.php';
?>

<style>
/* Override all existing styles */
body {
    padding-top: 0 !important;
    margin: 0 !important;
}

/* Modern Hero Section */
.hero-modern {
    position: relative !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    overflow: hidden !important;
    padding: 80px 0 !important;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 25%, #f9fafb 50%, #eff6ff 75%, #dbeafe 100%) !important;
    margin: 0 !important;
}

.hero-content-modern {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 48px !important;
    align-items: center !important;
    position: relative !important;
    z-index: 1 !important;
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding: 0 16px !important;
}

@media (min-width: 1024px) {
    .hero-content-modern {
        grid-template-columns: 1fr 1fr !important;
        gap: 64px !important;
        padding: 0 32px !important;
    }
}

.hero-text {
    text-align: center !important;
}

@media (min-width: 1024px) {
    .hero-text {
        text-align: left !important;
    }
}

.hero-badge {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    background: #dbeafe !important;
    color: #1d4ed8 !important;
    border-radius: 50px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 24px !important;
}

.hero-title-modern {
    font-size: 36px !important;
    font-weight: 700 !important;
    line-height: 1.25 !important;
    color: #111827 !important;
    margin-bottom: 24px !important;
    font-family: 'Poppins', sans-serif !important;
}

@media (min-width: 768px) {
    .hero-title-modern {
        font-size: 48px !important;
    }
}

@media (min-width: 1024px) {
    .hero-title-modern {
        font-size: 60px !important;
    }
}

.text-gradient {
    background: linear-gradient(135deg, #2563eb, #1e40af) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.hero-description-modern {
    font-size: 18px !important;
    line-height: 1.625 !important;
    color: #4b5563 !important;
    margin-bottom: 32px !important;
    max-width: 600px !important;
}

.hero-stats {
    display: flex !important;
    gap: 32px !important;
    justify-content: center !important;
    margin-bottom: 32px !important;
}

@media (min-width: 1024px) {
    .hero-stats {
        justify-content: flex-start !important;
    }
}

.stat-item {
    text-align: center !important;
}

.stat-number {
    display: block !important;
    font-size: 24px !important;
    font-weight: 700 !important;
    color: #2563eb !important;
    line-height: 1 !important;
}

.stat-label {
    display: block !important;
    font-size: 14px !important;
    color: #6b7280 !important;
    margin-top: 4px !important;
}

.hero-actions-modern {
    display: flex !important;
    gap: 16px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

@media (min-width: 1024px) {
    .hero-actions-modern {
        justify-content: flex-start !important;
    }
}

.btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    padding: 16px 32px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    line-height: 1 !important;
    border: 1px solid transparent !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.15s ease !important;
    text-decoration: none !important;
    white-space: nowrap !important;
}

.btn-primary {
    background-color: #2563eb !important;
    color: white !important;
    border-color: #2563eb !important;
}

.btn-primary:hover {
    background-color: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.btn-outline {
    background-color: transparent !important;
    color: #2563eb !important;
    border-color: #2563eb !important;
}

.btn-outline:hover {
    background-color: #eff6ff !important;
    color: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
}

.hero-visual {
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.hero-video-modern {
    position: relative !important;
    width: 100% !important;
    max-width: 600px !important;
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.hero-video-modern video {
    width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* Services Section */
.services-modern {
    padding: 80px 0 !important;
    background: #f9fafb !important;
}

.container {
    width: 100% !important;
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding: 0 16px !important;
}

.section-header {
    text-align: center !important;
    margin-bottom: 64px !important;
}

.section-badge {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    background: #dbeafe !important;
    color: #1d4ed8 !important;
    border-radius: 50px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 16px !important;
}

.section-title {
    font-size: 36px !important;
    font-weight: 700 !important;
    line-height: 1.25 !important;
    color: #111827 !important;
    margin-bottom: 16px !important;
    font-family: 'Poppins', sans-serif !important;
}

.section-description {
    font-size: 18px !important;
    color: #4b5563 !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

.services-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 24px !important;
    margin-bottom: 64px !important;
}

.service-card {
    background: white !important;
    border-radius: 16px !important;
    padding: 32px !important;
    text-decoration: none !important;
    color: inherit !important;
    transition: all 0.3s ease !important;
    border: 1px solid #e5e7eb !important;
    position: relative !important;
    overflow: hidden !important;
}

.service-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
    border-color: #bfdbfe !important;
}

.service-icon {
    width: 64px !important;
    height: 64px !important;
    border-radius: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 16px !important;
    font-size: 24px !important;
}

.service-icon-primary {
    background: #dbeafe !important;
    color: #2563eb !important;
}

.service-icon-success {
    background: #dcfce7 !important;
    color: #16a34a !important;
}

.service-icon-warning {
    background: #fef3c7 !important;
    color: #d97706 !important;
}

.service-icon-error {
    background: #fee2e2 !important;
    color: #dc2626 !important;
}

.service-icon-info {
    background: #dbeafe !important;
    color: #2563eb !important;
}

.service-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 8px !important;
}

.service-description {
    font-size: 16px !important;
    color: #4b5563 !important;
    line-height: 1.625 !important;
    margin-bottom: 16px !important;
}

.service-arrow {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    color: #2563eb !important;
    font-size: 18px !important;
    transition: transform 0.15s ease !important;
}

.service-card:hover .service-arrow {
    transform: translateX(4px) !important;
}

.services-cta {
    padding: 32px !important;
    background: white !important;
    border-radius: 16px !important;
    border: 2px dashed #d1d5db !important;
}

.cta-text {
    font-size: 18px !important;
    color: #4b5563 !important;
    margin-bottom: 16px !important;
}

.text-center {
    text-align: center !important;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0 !important;
    background: white !important;
}

.steps-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 32px !important;
    margin-top: 64px !important;
}

.step-card {
    text-align: center !important;
    position: relative !important;
    padding: 32px 24px !important;
}

.step-number {
    position: absolute !important;
    top: -20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 40px !important;
    height: 40px !important;
    background: #2563eb !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 700 !important;
    font-size: 18px !important;
}

.step-icon {
    width: 80px !important;
    height: 80px !important;
    background: #eff6ff !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 24px !important;
    font-size: 32px !important;
    color: #2563eb !important;
}

.step-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 12px !important;
}

.step-description {
    font-size: 16px !important;
    color: #4b5563 !important;
    line-height: 1.6 !important;
}

/* Features Section */
.features-section {
    padding: 80px 0 !important;
    background: #f9fafb !important;
}

.features-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 64px !important;
    align-items: center !important;
}

@media (min-width: 1024px) {
    .features-content {
        grid-template-columns: 1fr 1fr !important;
    }
}

.features-list {
    margin-top: 32px !important;
}

.feature-item {
    display: flex !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
}

.feature-icon {
    width: 48px !important;
    height: 48px !important;
    background: #dbeafe !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    color: #2563eb !important;
    flex-shrink: 0 !important;
}

.feature-content h4 {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 4px !important;
}

.feature-content p {
    font-size: 14px !important;
    color: #4b5563 !important;
    margin: 0 !important;
}

.features-visual {
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.feature-card, .stats-card {
    background: white !important;
    border-radius: 16px !important;
    padding: 24px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
    position: absolute !important;
}

.feature-card {
    width: 280px !important;
    top: 0 !important;
    left: 0 !important;
}

.stats-card {
    width: 240px !important;
    bottom: 0 !important;
    right: 0 !important;
}

.floating {
    animation: float 3s ease-in-out infinite !important;
}

.card-header {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-bottom: 16px !important;
}

.user-avatar {
    width: 40px !important;
    height: 40px !important;
    background: #3b82f6 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-weight: 600 !important;
}

.user-info h5 {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.user-info span {
    font-size: 12px !important;
    color: #6b7280 !important;
}

.rating {
    margin-left: auto !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    color: #fbbf24 !important;
    font-size: 12px !important;
}

.stat-row {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 8px !important;
    font-size: 14px !important;
}

.stat-value {
    font-weight: 600 !important;
    color: #2563eb !important;
}

/* Advanced Search Section */
.advanced-search {
    padding: 80px 0 !important;
    background: white !important;
}

.search-tools {
    max-width: 800px !important;
    margin: 0 auto !important;
}

.search-form {
    background: white !important;
    border-radius: 16px !important;
    padding: 32px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 32px !important;
}

.search-input-group {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
    position: relative !important;
}

.search-input-group i {
    position: absolute !important;
    left: 16px !important;
    color: #6b7280 !important;
    font-size: 18px !important;
}

.search-input {
    flex: 1 !important;
    padding: 16px 16px 16px 48px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    transition: all 0.15s ease !important;
}

.search-input:focus {
    outline: none !important;
    border-color: #2563eb !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

.search-btn {
    padding: 16px 24px !important;
    white-space: nowrap !important;
}

.search-filters {
    display: flex !important;
    gap: 16px !important;
    flex-wrap: wrap !important;
}

.filter-select {
    padding: 12px 16px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    background: white !important;
    min-width: 150px !important;
}

.filter-btn {
    padding: 12px 16px !important;
    font-size: 14px !important;
}

.popular-searches {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    flex-wrap: wrap !important;
}

.popular-label {
    font-size: 14px !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.popular-tags {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.tag {
    padding: 6px 12px !important;
    background: #f3f4f6 !important;
    color: #374151 !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    text-decoration: none !important;
    transition: all 0.15s ease !important;
}

.tag:hover {
    background: #e5e7eb !important;
    color: #111827 !important;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0 !important;
    background: #f9fafb !important;
}

.testimonials-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    gap: 24px !important;
    margin-top: 64px !important;
}

.testimonial-card {
    background: white !important;
    border-radius: 16px !important;
    padding: 24px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.testimonial-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

.testimonial-header {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-bottom: 16px !important;
}

.testimonial-content p {
    font-size: 14px !important;
    color: #4b5563 !important;
    line-height: 1.6 !important;
    margin: 0 !important;
}

/* CTA Section */
.cta-section {
    padding: 80px 0 !important;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    color: white !important;
}

.cta-content {
    text-align: center !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

.cta-text h2 {
    font-size: 36px !important;
    font-weight: 700 !important;
    margin-bottom: 16px !important;
    color: white !important;
}

.cta-text p {
    font-size: 18px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin-bottom: 32px !important;
}

.cta-actions {
    display: flex !important;
    gap: 16px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.cta-actions .btn-outline {
    background-color: transparent !important;
    color: white !important;
    border-color: white !important;
}

.cta-actions .btn-outline:hover {
    background-color: white !important;
    color: #2563eb !important;
    border-color: white !important;
}

@media (max-width: 768px) {
    .hero-modern {
        min-height: 80vh !important;
        padding: 64px 0 !important;
    }

    .hero-title-modern {
        font-size: 30px !important;
    }

    .hero-description-modern {
        font-size: 16px !important;
    }

    .hero-stats {
        gap: 16px !important;
    }

    .hero-actions-modern {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .search-input-group {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .search-filters {
        flex-direction: column !important;
    }

    .filter-select {
        min-width: auto !important;
    }

    .cta-actions {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .features-visual {
        display: none !important;
    }
}
</style>

<script>
// Enhanced Homepage Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    const filterSelects = document.querySelectorAll('.filter-select');

    // Search button click
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `browse_services.php?search=${encodeURIComponent(query)}`;
            }
        });
    }

    // Search on Enter key
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    }

    // Popular tags functionality
    const popularTags = document.querySelectorAll('.tag');
    popularTags.forEach(tag => {
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            const tagText = this.textContent.trim();
            if (searchInput) {
                searchInput.value = tagText;
                searchBtn.click();
            }
        });
    });

    // Advanced filters toggle
    const filterBtn = document.querySelector('.filter-btn');
    if (filterBtn) {
        filterBtn.addEventListener('click', function() {
            // Toggle advanced filters visibility
            const filters = document.querySelector('.search-filters');
            if (filters.style.display === 'none') {
                filters.style.display = 'flex';
                this.innerHTML = '<i class="ri-filter-line"></i> Masquer filtres';
            } else {
                filters.style.display = 'none';
                this.innerHTML = '<i class="ri-filter-line"></i> Filtres avancés';
            }
        });
    }

    // Smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading states to buttons
    const ctaButtons = document.querySelectorAll('.btn');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.href && !this.href.includes('#')) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="ri-loader-4-line"></i> Chargement...';
                this.style.pointerEvents = 'none';

                // Reset after 3 seconds if page doesn't change
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.pointerEvents = 'auto';
                }, 3000);
            }
        });
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe sections for scroll animations
    const sections = document.querySelectorAll('.services-modern, .how-it-works, .features-section, .advanced-search, .testimonials-section, .cta-section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'all 0.6s ease';
        observer.observe(section);
    });

    // Add hover effects to service cards
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Stats counter animation
    const statNumbers = document.querySelectorAll('.stat-number');
    const animateStats = () => {
        statNumbers.forEach(stat => {
            const target = parseInt(stat.textContent.replace(/\D/g, ''));
            const suffix = stat.textContent.replace(/\d/g, '');
            let current = 0;
            const increment = target / 50;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                stat.textContent = Math.floor(current) + suffix;
            }, 30);
        });
    };

    // Trigger stats animation when hero is visible
    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStats();
                heroObserver.unobserve(entry.target);
            }
        });
    });

    const heroStats = document.querySelector('.hero-stats');
    if (heroStats) {
        heroObserver.observe(heroStats);
    }
});
</script>

<!-- Loading Overlay -->
<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.95); z-index: 9999; display: flex; align-items: center; justify-content: center; opacity: 1; transition: opacity 0.5s ease;">
    <div style="text-align: center;">
        <div style="width: 50px; height: 50px; border: 3px solid #e5e7eb; border-top: 3px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
        <p style="color: #6b7280; font-size: 14px; margin: 0;">Chargement de SawbLi...</p>
    </div>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// Hide loading overlay when page is fully loaded
window.addEventListener('load', function() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        setTimeout(() => {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 500);
        }, 500);
    }
});
</script>

<!-- Modern Hero Section -->
<section class="hero-modern">
    <div class="hero-content-modern">
        <div class="hero-text">
            <div class="hero-badge">
                <i class="ri-star-fill"></i>
                <span>Plateforme #1 au Maroc</span>
            </div>
            
            <h1 class="hero-title-modern">
                Connectez-vous avec les 
                <span class="text-gradient">meilleurs freelances</span> 
                marocains
            </h1>
            
            <p class="hero-description-modern">
                Découvrez des talents exceptionnels dans le développement web, marketing digital, 
                design graphique et bien plus. Réalisez vos projets avec des professionnels qualifiés.
            </p>
            
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">Freelances</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <span class="stat-label">Projets réalisés</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4.9/5</span>
                    <span class="stat-label">Satisfaction</span>
                </div>
            </div>
            
            <div class="hero-actions-modern">
                <a href="signup.php?type=freelancer" class="btn btn-primary">
                    <i class="ri-user-star-line"></i>
                    <span>Devenir freelance</span>
                </a>
                <a href="browse_services.php" class="btn btn-outline">
                    <i class="ri-search-line"></i>
                    <span>Trouver un service</span>
                </a>
            </div>
        </div>
        
        <div class="hero-visual">
            <div class="hero-video-modern">
                <video src="IMG/SawbLi.mp4" autoplay loop muted playsinline>
                    <source src="IMG/SawbLi.mp4" type="video/mp4">
                </video>
            </div>
        </div>
    </div>
</section>

<!-- Modern Services Section -->
<section class="services-modern">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="ri-service-line"></i>
                <span>Nos Services</span>
            </div>
            <h2 class="section-title">
                Explorez nos <span class="text-gradient">catégories de services</span>
            </h2>
            <p class="section-description">
                Découvrez une large gamme de services professionnels proposés par nos freelances experts
            </p>
        </div>

        <div class="services-grid">
            <?php
            // Fetch categories from database
            $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
            $categories = $stmt->fetchAll();

            $icons = [
                'Développement' => 'ri-code-line',
                'Marketing Digital' => 'ri-megaphone-line',
                'Graphisme' => 'ri-palette-line',
                'Montage Vidéo' => 'ri-movie-line',
                'Analyse de Données' => 'ri-bar-chart-line',
                'Musique & Voix Off' => 'ri-music-line',
                'Rédaction de Scripts' => 'ri-quill-pen-line',
                'Traduction' => 'ri-translate-line',
                'Cours en Ligne' => 'ri-graduation-cap-line'
            ];

            $colors = [
                'Développement' => 'primary',
                'Marketing Digital' => 'success',
                'Graphisme' => 'warning',
                'Montage Vidéo' => 'error',
                'Analyse de Données' => 'info',
                'Musique & Voix Off' => 'primary',
                'Rédaction de Scripts' => 'success',
                'Traduction' => 'warning',
                'Cours en Ligne' => 'error'
            ];

            foreach ($categories as $category) {
                $icon = $icons[$category['name']] ?? 'ri-service-line';
                $color = $colors[$category['name']] ?? 'primary';

                echo '<a href="browse_services.php?category=' . urlencode($category['name']) . '" class="service-card">';
                echo '<div class="service-icon service-icon-' . $color . '">';
                echo '<i class="' . $icon . '"></i>';
                echo '</div>';
                echo '<h3 class="service-title">' . htmlspecialchars($category['name']) . '</h3>';
                echo '<p class="service-description">Découvrez nos experts en ' . strtolower(htmlspecialchars($category['name'])) . '</p>';
                echo '<div class="service-arrow">';
                echo '<i class="ri-arrow-right-line"></i>';
                echo '</div>';
                echo '</a>';
            }
            ?>
        </div>

        <div class="services-cta text-center">
            <p class="cta-text">Vous ne trouvez pas ce que vous cherchez ?</p>
            <a href="browse_services.php" class="btn btn-outline btn-lg">
                <i class="ri-search-line"></i>
                <span>Voir tous les services</span>
            </a>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="ri-lightbulb-line"></i>
                <span>Comment ça marche</span>
            </div>
            <h2 class="section-title">
                Commencez en <span class="text-gradient">3 étapes simples</span>
            </h2>
            <p class="section-description">
                Rejoignez notre plateforme et commencez à travailler avec les meilleurs freelances du Maroc
            </p>
        </div>

        <div class="steps-grid">
            <div class="step-card">
                <div class="step-number">1</div>
                <div class="step-icon">
                    <i class="ri-user-add-line"></i>
                </div>
                <h3 class="step-title">Créez votre compte</h3>
                <p class="step-description">Inscrivez-vous gratuitement en tant que client ou freelance en quelques minutes</p>
            </div>

            <div class="step-card">
                <div class="step-number">2</div>
                <div class="step-icon">
                    <i class="ri-search-2-line"></i>
                </div>
                <h3 class="step-title">Trouvez le bon talent</h3>
                <p class="step-description">Parcourez les profils, consultez les portfolios et choisissez le freelance parfait</p>
            </div>

            <div class="step-card">
                <div class="step-number">3</div>
                <div class="step-icon">
                    <i class="ri-rocket-line"></i>
                </div>
                <h3 class="step-title">Lancez votre projet</h3>
                <p class="step-description">Collaborez facilement et recevez des résultats de qualité professionnelle</p>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-content">
            <div class="features-text">
                <div class="section-badge">
                    <i class="ri-star-line"></i>
                    <span>Fonctionnalités</span>
                </div>
                <h2 class="section-title">
                    Pourquoi choisir <span class="text-gradient">SawbLi</span> ?
                </h2>
                <p class="section-description">
                    Notre plateforme offre tous les outils nécessaires pour réussir vos projets freelance
                </p>

                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="ri-shield-check-line"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Paiements sécurisés</h4>
                            <p>Système de paiement protégé avec garantie de satisfaction</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="ri-customer-service-2-line"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Support 24/7</h4>
                            <p>Équipe de support disponible pour vous aider à tout moment</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="ri-award-line"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Freelances vérifiés</h4>
                            <p>Tous nos freelances sont vérifiés et évalués par la communauté</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="features-visual">
                <div class="feature-card floating">
                    <div class="card-header">
                        <div class="user-avatar"></div>
                        <div class="user-info">
                            <h5>Ahmed Benali</h5>
                            <span>Développeur Full Stack</span>
                        </div>
                        <div class="rating">
                            <i class="ri-star-fill"></i>
                            <span>4.9</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>"Excellent travail, livré dans les délais"</p>
                    </div>
                </div>

                <div class="stats-card floating">
                    <h4>Statistiques en temps réel</h4>
                    <div class="stat-row">
                        <span>Projets complétés</span>
                        <span class="stat-value">1,247</span>
                    </div>
                    <div class="stat-row">
                        <span>Taux de satisfaction</span>
                        <span class="stat-value">98.5%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Advanced Search Section -->
<section class="advanced-search">
    <div class="container">
        <div class="search-header text-center">
            <h2 class="section-title">Trouvez exactement ce que vous cherchez</h2>
            <p class="section-description">Utilisez nos outils de recherche avancée pour trouver le freelance parfait</p>
        </div>

        <div class="search-tools">
            <div class="search-form">
                <div class="search-input-group">
                    <i class="ri-search-line"></i>
                    <input type="text" placeholder="Que recherchez-vous ?" class="search-input">
                    <button class="search-btn btn btn-primary">
                        <i class="ri-search-line"></i>
                        Rechercher
                    </button>
                </div>

                <div class="search-filters">
                    <select class="filter-select">
                        <option value="">Toutes les catégories</option>
                        <option value="dev">Développement</option>
                        <option value="design">Design</option>
                        <option value="marketing">Marketing</option>
                    </select>

                    <select class="filter-select">
                        <option value="">Budget</option>
                        <option value="0-500">0 - 500 DH</option>
                        <option value="500-2000">500 - 2000 DH</option>
                        <option value="2000+">2000+ DH</option>
                    </select>

                    <select class="filter-select">
                        <option value="">Délai</option>
                        <option value="1">1 jour</option>
                        <option value="7">1 semaine</option>
                        <option value="30">1 mois</option>
                    </select>

                    <button class="filter-btn btn btn-outline">
                        <i class="ri-filter-line"></i>
                        Filtres avancés
                    </button>
                </div>
            </div>

            <div class="popular-searches">
                <span class="popular-label">Recherches populaires:</span>
                <div class="popular-tags">
                    <a href="#" class="tag">Logo Design</a>
                    <a href="#" class="tag">Site Web</a>
                    <a href="#" class="tag">Application Mobile</a>
                    <a href="#" class="tag">SEO</a>
                    <a href="#" class="tag">Rédaction</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="ri-chat-quote-line"></i>
                <span>Témoignages</span>
            </div>
            <h2 class="section-title">
                Ce que disent nos <span class="text-gradient">utilisateurs</span>
            </h2>
            <p class="section-description">
                Découvrez les expériences de nos clients et freelances satisfaits
            </p>
        </div>

        <div class="testimonials-grid">
            <?php
            // Default testimonials for demo
            $defaultTestimonials = [
                [
                    'name' => 'Fatima Zahra',
                    'type' => 'Cliente',
                    'rating' => 5,
                    'comment' => 'SawbLi a transformé ma façon de travailler avec les freelances. Interface intuitive et freelances de qualité.',
                    'color' => '#3b82f6'
                ],
                [
                    'name' => 'Youssef Alami',
                    'type' => 'Freelance',
                    'rating' => 5,
                    'comment' => 'Excellente plateforme pour trouver des clients sérieux. Les paiements sont sécurisés et rapides.',
                    'color' => '#10b981'
                ],
                [
                    'name' => 'Aicha Benali',
                    'type' => 'Cliente',
                    'rating' => 5,
                    'comment' => 'Service client exceptionnel et freelances très professionnels. Je recommande vivement !',
                    'color' => '#f59e0b'
                ]
            ];

            foreach ($defaultTestimonials as $testimonial) {
                echo '<div class="testimonial-card">';
                echo '<div class="testimonial-header">';
                echo '<div class="user-avatar" style="background: ' . $testimonial['color'] . '">';
                echo strtoupper(substr($testimonial['name'], 0, 1));
                echo '</div>';
                echo '<div class="user-info">';
                echo '<h5>' . $testimonial['name'] . '</h5>';
                echo '<span>' . $testimonial['type'] . '</span>';
                echo '</div>';
                echo '<div class="rating">';
                for ($i = 1; $i <= 5; $i++) {
                    echo '<i class="ri-star-fill"></i>';
                }
                echo '</div>';
                echo '</div>';
                echo '<div class="testimonial-content">';
                echo '<p>"' . $testimonial['comment'] . '"</p>';
                echo '</div>';
                echo '</div>';
            }
            ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <div class="cta-text">
                <h2>Prêt à commencer votre projet ?</h2>
                <p>Rejoignez des milliers d'utilisateurs satisfaits sur SawbLi</p>
            </div>
            <div class="cta-actions">
                <a href="signup.php?type=client" class="btn btn-primary btn-lg">
                    <i class="ri-user-line"></i>
                    Commencer en tant que client
                </a>
                <a href="signup.php?type=freelancer" class="btn btn-outline btn-lg">
                    <i class="ri-briefcase-line"></i>
                    Devenir freelance
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
