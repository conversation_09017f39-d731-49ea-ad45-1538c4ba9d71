<?php
include 'db.php';
$conn = $pdo;
include 'includes/header.php';
?>

<!-- Hero Video Section -->
<div class="hero-video-container">
    <video src="IMG/SawbLi.mp4" autoplay loop muted class="hero-video">
    </video>
</div>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">Travaillez avec les meilleurs freelances marocains 🚀</h1>
        <p class="hero-description">Découvrez des opportunités dans le développement web et le marketing digital.</p>
        <div class="hero-actions">
            <a href="signup.php?type=freelancer" class="cta-btn primary">
                <i class="ri-user-star-line"></i>
                <span>Rejoindre en tant que freelance</span>
            </a>
            <a href="browse_services.php" class="cta-btn secondary">
                <i class="ri-search-line"></i>
                <span>Parcourir les services</span>
            </a>
        </div>
    </div>
</section>

<style>
/* ========================================
   HOMEPAGE STYLES - CONSISTENT WITH HEADER
   ======================================== */

/* Hero Video Section */
.hero-video-container {
    display: flex;
    justify-content: center;
    margin: 80px auto 40px;
    padding: 0 2rem;
    max-width: 1200px;
}

.hero-video {
    width: 100%;
    max-width: 800px;
    height: auto;
    border: none;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-video:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
}

/* Hero Section */
.hero-section {
    padding: 60px 2rem 80px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    text-align: center;
}

.hero-content {
    max-width: 1000px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 3rem;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.2rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    min-width: 250px;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
}

.cta-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(19, 64, 116, 0.3);
    color: white;
    text-decoration: none;
}

.cta-btn.secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.cta-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    text-decoration: none;
}

.cta-btn i {
    font-size: 1.3rem;
}

/* ========================================
   RESPONSIVE DESIGN FOR HOMEPAGE
   ======================================== */

/* Large Desktop */
@media (min-width: 1200px) {
    .hero-video-container {
        margin: 100px auto 60px;
        padding: 0 3rem;
    }

    .hero-section {
        padding: 80px 3rem 100px;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .hero-description {
        font-size: 1.4rem;
    }

    .cta-btn {
        padding: 1.4rem 3rem;
        font-size: 1.2rem;
        min-width: 280px;
    }
}

/* Standard Desktop */
@media (min-width: 992px) and (max-width: 1199px) {
    .hero-video-container {
        margin: 90px auto 50px;
        padding: 0 2.5rem;
    }

    .hero-section {
        padding: 70px 2.5rem 90px;
    }

    .hero-title {
        font-size: 3.2rem;
    }

    .cta-btn {
        min-width: 260px;
    }
}

/* Tablet */
@media (min-width: 768px) and (max-width: 991px) {
    .hero-video-container {
        margin: 80px auto 40px;
        padding: 0 2rem;
    }

    .hero-section {
        padding: 60px 2rem 80px;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-description {
        font-size: 1.2rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-btn {
        width: 100%;
        max-width: 400px;
        min-width: auto;
    }
}

/* Mobile */
@media (max-width: 767px) {
    .hero-video-container {
        margin: 60px auto 30px;
        padding: 0 1rem;
    }

    .hero-video {
        border-radius: 15px;
    }

    .hero-section {
        padding: 40px 1rem 60px;
    }

    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-btn {
        width: 100%;
        min-width: auto;
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .hero-video-container {
        margin: 50px auto 20px;
        padding: 0 0.5rem;
    }

    .hero-section {
        padding: 30px 0.5rem 50px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .cta-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }

    .cta-btn i {
        font-size: 1.1rem;
    }
}

/* ========================================
   HEADER CONSISTENCY OVERRIDES
   ======================================== */

/* Ensure header works properly on homepage */
@media (min-width: 769px) {
    body {
        padding-top: 70px !important;
    }

    .professional-header {
        position: fixed !important;
        top: 0 !important;
        width: 100% !important;
        z-index: 1000 !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 50px !important;
    }

    .header-top-bar {
        display: none !important;
    }

    .main-navigation {
        display: none !important;
    }

    .search-section {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }
}

/* Animation for smooth loading */
.hero-video-container,
.hero-section {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Services Section -->
<section id="services" class="services">
    <h3>Nos Services</h3>
    <div class="service-container">
        <button id="prev" class="scroll-btn left">&#10094;</button>
        <div class="service-boxes">
            <?php
            // Fetch categories from database
            $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
            $categories = $stmt->fetchAll();
            
            $icons = [
                'Développement' => 'IMG/dev.png',
                'Marketing Digital' => 'IMG/marketing.png',
                'Graphisme' => 'IMG/graphisme.png',
                'Montage Vidéo' => 'IMG/edit.png',
                'Analyse de Données' => 'IMG/analysis.png',
                'Musique & Voix Off' => 'IMG/voice.png',
                'Rédaction de Scripts' => 'IMG/script.png',
                'Traduction' => 'IMG/translation.png',
                'Cours en Ligne' => 'IMG/cours.png'
            ];
            
            foreach ($categories as $category) {
                $icon = $icons[$category['name']] ?? 'default-icon.png'; // Set a default icon if no match
                
                // Display category with icon
                echo '<a href="browse_services.php?category=' . urlencode($category['name']) . '" class="service-box">';
                echo '<div class="category-icon">';
                echo '<img src="' . $icon . '" alt="' . htmlspecialchars($category['name']) . '" class="category-img">';
                echo '</div>';
                echo '<span>' . htmlspecialchars_decode($category['name']) . '</span>';
                echo '</a>';
            }
            ?>
        </div>
        <button id="next" class="scroll-btn right">&#10095;</button>
    </div>
</section>


<!-- Reviews Section -->
<section id="testimonials" class="testimonials">
    <h3>Avis de nos clients</h3>
    <div class="testimonial-cards">
        <?php
        // Get a few random reviews
        $stmt = $conn->query("
            SELECT r.rating, r.comment, u.full_name, u.user_type 
            FROM reviews r
            JOIN users u ON r.reviewer_id = u.id
            ORDER BY RAND()
            LIMIT 3
        ");
        
        $reviews = $stmt->fetchAll();
        
        if (count($reviews) > 0) {
            foreach ($reviews as $review) {
                echo '<div class="testimonial-card">';
                echo '<p>"' . htmlspecialchars_decode($review['comment']) . '"</p>';
                echo '<span>- ' . htmlspecialchars_decode($review['full_name']) . ', ' . 
                     ($review['user_type'] == 'client' ? 'client' : 'freelancer') . '</span>';
                echo '</div>';
            }
        } else {
            // Default testimonials if no reviews in database
            ?>
            <div class="testimonial-card">
                <p>"SawbLi a transformé ma carrière de freelance ! Les projets sont variés et les clients très
                    satisfaits."</p>
                <span>- Brahim, programmeur</span>
            </div>
            <div class="testimonial-card">
                <p>"Une plateforme simple et efficace pour trouver des freelances de qualité en marketing digital."</p>
                <span>- Lamyae, société de marketing</span>
            </div>
            <?php
        }
        ?>
    </div>
</section>

<!-- Stats Section -->
<section id="stats" class="stats">
    <h3>Statistiques SawbLi</h3>
    <div class="stat-boxes">
        <?php
        // Get actual stats from database
        $projectCount = $conn->query("SELECT COUNT(*) as count FROM projects")->fetch()['count'] ?? 0;
        $freelancerCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'freelancer'")->fetch()['count'] ?? 0;
        $clientCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")->fetch()['count'] ?? 0;
        
        // If stats are too low, use default values
        $projectCount = max($projectCount, 1200);
        $freelancerCount = max($freelancerCount, 300);
        $clientCount = max($clientCount, 500);
        ?>
        <div class="stat-box">
            <h4><?= $projectCount ?>+</h4>
            <p>Projets réalisés</p>
        </div>
        <div class="stat-box">
            <h4><?= $freelancerCount ?>+</h4>
            <p>Indépendants inscrits</p>
        </div>
        <div class="stat-box">
            <h4><?= $clientCount ?>+</h4>
            <p>Clients satisfaits</p>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="cta" class="cta">
    <h3>Êtes-vous prêt à devenir freelance ou à embaucher les meilleurs ?</h3>
    <p>Rejoignez SawbLi et commencez votre aventure maintenant !</p>
    <a href="signup.php" class="cta-btn">Rejoignez-nous</a>
</section>

<!-- Why Choose Us Section -->
<section id="why-us" class="why-us">
    <h3>Pourquoi choisir SawbLi ?</h3>
    <div class="reasons">
        <div class="reason-box">
            <h4>Facilité d'utilisation</h4>
            <p>Notre interface est simple et conviviale.</p>
        </div>
        <div class="reason-box">
            <h4>Assistance continue</h4>
            <p>Assistance technique 24h/24 et 7j/7 disponible pour tous les membres.</p>
        </div>
        <div class="reason-box">
            <h4>Haute fiabilité</h4>
            <p>Systèmes de sécurité pour assurer des transactions sécurisées.</p>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
