<?php
session_start();
require_once 'db.php';
require_once 'includes/auth.php';
$conn = $pdo;
// Ensure the user is logged in and is a client
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'client'");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    $_SESSION['error'] = "Vous n'avez pas l'autorisation d'accéder à cette page.";
    header("Location: index.php");
    exit;
}

// Get active projects
$stmt = $conn->prepare("
    SELECT p.*, u.username as freelancer_name, s.title as service_title
    FROM projects p
    JOIN users u ON p.freelancer_id = u.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.client_id = ? AND p.status != 'completed' AND p.status != 'cancelled'
    ORDER BY p.created_at DESC
");
$stmt->execute([$user_id]);
$active_projects = $stmt->fetchAll();

// Get completed projects
$stmt = $conn->prepare("
    SELECT p.*, u.username as freelancer_name, s.title as service_title
    FROM projects p
    JOIN users u ON p.freelancer_id = u.id
    LEFT JOIN services s ON p.service_id = s.id
    WHERE p.client_id = ? AND p.status = 'completed'
    ORDER BY p.completion_date DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$completed_projects = $stmt->fetchAll();

// Get reviews given
$stmt = $conn->prepare("
    SELECT r.*, p.title as project_title, u.username as reviewee_name
    FROM reviews r
    JOIN projects p ON r.project_id = p.id
    JOIN users u ON r.reviewee_id = u.id
    WHERE r.reviewer_id = ?
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$user_id]);
$reviews = $stmt->fetchAll();

// Get recently viewed services
$stmt = $conn->prepare("
    SELECT s.*, u.username as freelancer_name
    FROM services s
    JOIN users u ON s.user_id = u.id
    ORDER BY RAND()
    LIMIT 6
");
$stmt->execute();
$suggested_services = $stmt->fetchAll();

// Calculate statistics
$total_spent = $conn->prepare("
    SELECT SUM(price) as total
    FROM projects
    WHERE client_id = ? AND status = 'completed'
");
$total_spent->execute([$user_id]);
$spent = $total_spent->fetch()['total'] ?? 0;

$total_projects = $conn->prepare("
    SELECT COUNT(*) as count
    FROM projects
    WHERE client_id = ?
");
$total_projects->execute([$user_id]);
$projects_count = $total_projects->fetch()['count'] ?? 0;

include 'includes/header.php';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dashboard Client - SawbLi</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 text-gray-800">

  <!-- Dashboard Content -->
  <div class="dashboard-container">
    <!-- Welcome Header -->
    <div class="dashboard-header">
      <h2>Bienvenue sur votre tableau de bord, <?= htmlspecialchars($user['full_name']) ?> !</h2>
      <p>Trouvez des freelancers, gérez vos projets et suivez vos collaborations.</p>
    </div>
    
    <!-- Stats Section -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <h3><?= number_format($projects_count) ?></h3>
        <p>Projets totaux</p>
      </div>
      <div class="stat-card">
        <h3><?= number_format($spent, 2) ?> DH</h3>
        <p>Dépenses totales</p>
      </div>
      <div class="stat-card">
        <h3><?= count($active_projects) ?></h3>
        <p>Projets en cours</p>
      </div>
      <div class="stat-card">
        <h3><?= count($completed_projects) ?></h3>
        <p>Projets terminés</p>
      </div>
    </div>
    
    <!-- Projects Section -->
    <div class="mt-10 dashboard-content">
      <!-- Active Projects -->
      <div class="dashboard-card">
        <h3>Projets en cours</h3>
        <div class="dashboard-card-content">
          <?php if (count($active_projects) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($active_projects as $project): ?>
              <li class="py-3">
                <p class="font-semibold"><?= htmlspecialchars($project['title']) ?></p>
                <p class="text-sm">Freelancer: <?= htmlspecialchars($project['freelancer_name']) ?></p>
                <p class="text-sm">Prix: <?= number_format($project['price'], 2) ?> DH</p>
                <p class="text-sm">Statut: 
                  <span class="px-2 py-1 rounded text-xs
                    <?= $project['status'] === 'in_progress' ? 'bg-blue-100 text-blue-700' : 'bg-yellow-100 text-yellow-700' ?>">
                    <?= $project['status'] === 'in_progress' ? 'En cours' : 'En attente' ?>
                  </span>
                </p>
                <a href="view_project.php?id=<?= $project['id'] ?>" class="text-blue-600 text-sm mt-2 inline-block">Voir détails →</a>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Aucun projet en cours.</p>
            <div class="text-center mt-2">
              <a href="browse_services.php" class="text-blue-600">Trouvez des freelancers maintenant →</a>
            </div>
          <?php endif; ?>
        </div>
      </div>
      
      <!-- Completed Projects -->
      <div class="dashboard-card">
        <h3>Projets terminés</h3>
        <div class="dashboard-card-content">
          <?php if (count($completed_projects) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($completed_projects as $project): ?>
              <li class="py-3">
                <p class="font-semibold"><?= htmlspecialchars($project['title']) ?></p>
                <p class="text-sm">Freelancer: <?= htmlspecialchars($project['freelancer_name']) ?></p>
                <p class="text-sm">Prix: <?= number_format($project['price'], 2) ?> DH</p>
                <p class="text-sm">Terminé le: <?= date('d/m/Y', strtotime($project['completion_date'])) ?></p>
                <a href="view_project.php?id=<?= $project['id'] ?>" class="text-blue-600 text-sm mt-2 inline-block">Voir détails →</a>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Aucun projet terminé.</p>
          <?php endif; ?>
        </div>
      </div>
      
      <!-- Reviews -->
      <div class="dashboard-card">
        <h3>Avis que vous avez donnés</h3>
        <div class="dashboard-card-content">
          <?php if (count($reviews) > 0): ?>
            <ul class="divide-y">
              <?php foreach ($reviews as $review): ?>
              <li class="py-3">
                <div class="flex justify-between">
                  <p class="font-semibold"><?= htmlspecialchars($review['project_title']) ?></p>
                  <p class="text-yellow-500">
                    <?php 
                    for ($i = 1; $i <= 5; $i++) {
                      echo $i <= $review['rating'] ? '★' : '☆';
                    }
                    ?>
                  </p>
                </div>
                <p class="text-sm italic">"<?= htmlspecialchars($review['comment']) ?>"</p>
                <p class="text-sm">À: <?= htmlspecialchars($review['reviewee_name']) ?></p>
              </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p class="text-center py-4">Vous n'avez donné aucun avis.</p>
          <?php endif; ?>
        </div>
      </div>
    </div>
    
    <!-- Suggested Services -->
    <div class="mt-10">
      <h3 class="text-2xl font-bold mb-6">Services suggérés pour vous</h3>
      
      <?php if (count($suggested_services) > 0): ?>
      <div class="services-list">
        <?php foreach ($suggested_services as $service): ?>
        <div class="service-card">
          <div class="service-card-header">
            <h3><?= htmlspecialchars($service['title']) ?></h3>
            <p class="text-sm text-gray-500">Par: <?= htmlspecialchars($service['freelancer_name']) ?></p>
          </div>
          <div class="service-card-body">
            <p><?= nl2br(htmlspecialchars(substr($service['description'], 0, 100) . (strlen($service['description']) > 100 ? '...' : ''))) ?></p>
            <p class="mt-2 text-sm">Catégorie: <?= htmlspecialchars($service['category']) ?></p>
          </div>
          <div class="service-card-footer">
            <span class="service-price"><?= number_format($service['price'], 2) ?> DH</span>
            <div class="service-actions">
              <a href="view_service.php?id=<?= $service['id'] ?>" class="btn btn-primary">Voir détails</a>
            </div>
          </div>
        </div>
        <?php endforeach; ?>
      </div>
      
      <div class="mt-6 text-center">
        <a href="browse_services.php" class="btn btn-primary">Voir plus de services</a>
      </div>
      <?php else: ?>
      <div class="bg-white p-6 rounded-lg shadow text-center">
        <p class="mb-4">Aucun service suggéré pour le moment.</p>
        <a href="browse_services.php" class="btn btn-primary">Parcourir tous les services</a>
      </div>
      <?php endif; ?>
    </div>
  </div>

<?php include 'includes/footer.php'; ?>
