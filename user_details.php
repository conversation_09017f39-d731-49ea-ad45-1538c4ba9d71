<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Vérifier si l'utilisateur est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: admin_login.php");
    exit;
}

// Initialiser les variables
$user = [];
$projects = [];
$services = [];
$earnings = ['total_earnings' => 0];

if (isset($_GET['user_id']) && !empty($_GET['user_id'])) {
    $user_id = (int)$_GET['user_id'];

    try {
        // Récupérer les informations de l'utilisateur
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = :user_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch();

        if (!$user) {
            throw new Exception("Utilisateur introuvable.");
        }

        // Récupérer les projets de l'utilisateur
        $projects_stmt = $conn->prepare("SELECT * FROM projects WHERE freelancer_id = :user_id");
        $projects_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $projects_stmt->execute();
        $projects = $projects_stmt->fetchAll();

        // Récupérer les services de l'utilisateur
        $services_stmt = $conn->prepare("SELECT * FROM services WHERE user_id = :user_id");
        $services_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $services_stmt->execute();
        $services = $services_stmt->fetchAll();

        // Calculer le montant total gagné par l'utilisateur
        $earnings_stmt = $conn->prepare("SELECT SUM(amount) AS total_earnings FROM transactions WHERE freelancer_id = :user_id AND transaction_type = 'income'");
        $earnings_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $earnings_stmt->execute();
        $earnings = $earnings_stmt->fetch();



    } catch (PDOException $e) {
        echo "Erreur PDO: " . htmlspecialchars($e->getMessage());
        exit;
    } catch (Exception $e) {
        echo "Erreur: " . htmlspecialchars($e->getMessage());
        exit;
    }
} else {
    echo "ID d'utilisateur non spécifié.";
    exit;
}

include 'includes/admin_header.php'; // Inclure l'entête de l'admin
?>

<h2 class="text-2xl font-bold mb-6">Détails de l'utilisateur : <?= htmlspecialchars($user['full_name']) ?></h2>

<div class="bg-white shadow-md rounded-lg p-6">
    <p><strong>Nom d'utilisateur :</strong> <?= htmlspecialchars($user['username']) ?></p>
    <p><strong>Email :</strong> <?= htmlspecialchars($user['email']) ?></p>
    <p><strong>Nom complet :</strong> <?= htmlspecialchars($user['full_name']) ?></p>

    <h3 class="text-xl font-semibold mt-6">Projets réalisés</h3>
    <ul class="list-disc pl-6">
        <?php if (!empty($projects)): ?>
            <?php foreach ($projects as $project): ?>
                <li><?= htmlspecialchars($project['title']) ?> - <?= htmlspecialchars($project['status']) ?></li>
            <?php endforeach; ?>
        <?php else: ?>
            <li>Aucun projet trouvé.</li>
        <?php endif; ?>
    </ul>

    <h3 class="text-xl font-semibold mt-6">Services publiés</h3>
    <ul class="list-disc pl-6">
        <?php if (!empty($services)): ?>
            <?php foreach ($services as $service): ?>
                <li><?= htmlspecialchars($service['title']) ?> - <?= htmlspecialchars($service['description']) ?></li>
            <?php endforeach; ?>
        <?php else: ?>
            <li>Aucun service trouvé.</li>
        <?php endif; ?>
    </ul>

    <h3 class="text-xl font-semibold mt-6">Total des gains</h3>
    <p><?= number_format($earnings['total_earnings'] ?? 0, 2) ?> DH</p>
</div>

<?php include 'includes/admin_footer.php'; // Inclure le pied de page ?>
