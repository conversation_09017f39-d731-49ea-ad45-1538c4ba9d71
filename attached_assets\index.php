<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SawbLi - Plateforme de Freelance Marocaine</title>
    <link rel="stylesheet" href="index.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />
</head>
<body>
    <!-- Navbar & Header Fixed -->
    <header class="header">
        <div class="logo">SawbLi<span class="dot">.</span></div>
        <input type="text" class="search" placeholder="Quel service recherchez-vous ?">
        <nav>
            <a href="#">SawbLi Pro</a>
            <div class="discover-dropdown">
                <a href="#" id="discover-btn">Découvrir</a>
                <ul class="discover-menu" id="discover-menu">
                    <li><a href="#services">🛠 Nos Services</a></li>
                    <li><a href="#testimonials">⭐️ Avis Clients</a></li>
                    <li><a href="#why-us">❓ Pourquoi SawbLi</a></li>
                    <li><a href="#stats">📊 Statistiques</a></li>
                </ul>
            </div>
            <div class="language-dropdown">
                <a href="#" id="language-btn">Français</a>
                <ul class="language-menu" id="language-menu">
                    <li><a href="?lang=fr">🇫🇷 Français</a></li>
                    <li><a href="?lang=ar">🇲🇦 العربية</a></li>
                    <li><a href="?lang=en">🇬🇧 English</a></li>
                </ul>
            </div>
            <a href="#">Devenir freelance</a>
            <a href="#" id="show-login-btn" class="show-login-btn">Se connecter</a>
            <!-- Section modale de connexion -->
            <section id="login" class="login" style="display: none;">
                <div class="login-overlay">
                    <!-- Bouton pour fermer la section -->
                    <button id="close-login-btn" class="close-login-btn">x</button>
                    <div class="login-container">
                        <h2>connecter-vous</h2>
                        <form method="POST" action="login.php">
                            <div class="form-group">
                                <label>Email :</label>
                                <input type="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label>Mot de passe :</label>
                                <input type="password" name="password" required>
                            </div>
                            <button type="submit" class="login-btn">Connexion</button>
                        </form>

                        <?php if (!empty($error)) echo "<p style='color:red;'>$error</p>"; ?>

                        <div class="forgot-password">
                            <a href="#">Mot de passe oublié ?</a>
                        </div>
                        <div class="signup-link">
                            <p>Vous n'avez pas de compte ? <a href="#signup">S'inscrire</a></p>
                        </div>
                    </div>
                </div>
            </section>
            <button class="show-signup-btn" id="show-signup-btn">S'inscrire</button>
            <!-- Section modale d'inscription -->
            <section id="signup" class="signup" style="display: none;">
                <div class="signup-overlay">
                    <!-- Bouton pour fermer la section -->
                    <button id="close-signup-btn" class="close-signup-btn">x</button>
                    <div class="signup-container">
                        <h2>Créer un compte</h2>
                        <form method="POST" action="signup.php">
                            <input type="text" name="username" placeholder="Nom d'utilisateur" required>
                            <input type="text" name="full_name" placeholder="Nom complet" required>
                            <input type="email" name="email" placeholder="Email" required>
                            <input type="password" name="password" placeholder="Mot de passe" required>
                            <input type="password" name="confirm_password" placeholder="Confirmer le mot de passe" required>

                            <select name="user_type" required>
                                <option value="">Choisissez un type</option>
                                <option value="client">Client</option>
                                <option value="freelancer">Freelancer</option>
                            </select>

                            <button type="submit">Créer un compte</button>
                        </form>


                        <?php if (!empty($error)) echo "<p style='color:red;'>$error</p>"; ?>

                        <div class="login-link">
                            <p>Vous avez déjà un compte ? <a href="#login">Se connecter</a></p>
                        </div>

                    </div>
                </div>
            </section>
        </nav>
    </header>
    <div class="hero-right">
        <video src="IMG/SawbLi.mp4" autoplay loop muted></video>
    </div>
    <!-- Hero Section -->
    <header class="hero">
        <div class="hero-left">
            <!-- Search Bar -->
            <div class="search-container" id="searchContainer">
                <input type="text" class="search-bar" placeholder="Rechercher des freelances, services...">
                <button class="search-button"><i class="ri-search-2-line"></i></button>
            </div>
            <h2>Travaillez avec les meilleurs freelances marocains 🚀</h2>
            <p>Découvrez des opportunités dans le développement web et le marketing digital.</p>
            <a href="#signup" class="cta-btn">Rejoindre en tant que freelance</a>
        </div>
    </header>
    <section id="services" class="services">
        <h3>Nos Services</h3>
        <div class="service-container">
            <button id="prev" class="scroll-btn left">&#10094;</button>
            <div class="service-boxes">
                <a href="developpement.html" class="service-box">
                    <img src="IMG/dev.png" alt="Développement">
                    <span>💻 Développement</span>
                </a>
                <a href="marketing.html" class="service-box">
                    <img src="IMG/marketing.png" alt="Marketing Digital">
                    <span>📢 Marketing Digital</span>
                </a>
                <a href="graphisme.html" class="service-box">
                    <img src="IMG/graphisme.png" alt="Graphisme">
                    <span>🎨 Graphisme</span>
                </a>
                <a href="montage.html" class="service-box">
                    <img src="IMG/edit.png" alt="Montage Vidéo">
                    <span>🎬 Montage Vidéo</span>
                </a>
                <a href="analyse.html" class="service-box">
                    <img src="IMG/analysis.png" alt="Analyse de Données">
                    <span>📊 Analyse de Données</span>
                </a>
                <a href="musique.html" class="service-box">
                    <img src="IMG/voice.png" alt="Musique & Voix Off">
                    <span>🎵 Musique & Voix Off</span>
                </a>
                <a href="redaction.html" class="service-box">
                    <img src="IMG/script.png" alt="Rédaction de Scripts">
                    <span>📝 Rédaction de Scripts</span>
                </a>
                <a href="traduction.html" class="service-box">
                    <img src="IMG/translation.png" alt="Traduction">
                    <span>🌍 Traduction</span>
                </a>
                <a href="cours.html" class="service-box">
                    <img src="IMG/cours.png" alt="Cours en Ligne">
                    <span>📚 Cours en Ligne</span>
                </a>
            </div>
            <button id="next" class="scroll-btn right">&#10095;</button>
        </div>
    </section>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const serviceContainer = document.querySelector(".service-boxes");
            const prevBtn = document.getElementById("prev");
            const nextBtn = document.getElementById("next");

            prevBtn.addEventListener("click", function () {
                serviceContainer.scrollBy({ left: -300, behavior: "smooth" });
            });

            nextBtn.addEventListener("click", function () {
                serviceContainer.scrollBy({ left: 300, behavior: "smooth" });
            });
        });
    </script>

    <!-- Reviews Section -->
    <section id="testimonials" class="testimonials">
        <h3>Avis de nos clients</h3>
        <div class="testimonial-cards">
            <div class="testimonial-card">
                <p>"SawbLi a transformé ma carrière de freelance ! Les projets sont variés et les clients très
                    satisfaits."</p>
                <span>- Brahim, programmeur</span>
            </div>
            <div class="testimonial-card">
                <p>"Une plateforme simple et efficace pour trouver des freelances de qualité en marketing digital."</p>
                <span>- Lamyae, société de marketing</span>
            </div>
        </div>
    </section>
    <section id="stats" class="stats">
        <h3>Statistiques SawbLi</h3>
        <div class="stat-boxes">
            <div class="stat-box">
                <h4>1200+</h4>
                <p>Projet réalisé</p>
            </div>
            <div class="stat-box">
                <h4>300+</h4>
                <p>Indépendants inscrits</p>
            </div>
            <div class="stat-box">
                <h4>500+</h4>
                <p>Clients satisfaits</p>
            </div>
        </div>
    </section>
    <section id="cta" class="cta">
        <h3>Êtes-vous prêt à devenir freelance ou à embaucher les meilleurs ?</h3>
        <p>Rejoignez SawbLi et commencez votre aventure maintenant !</p>
        <a href="#signup" class="cta-btn">Rejoignez</a>
    </section>
    <section id="why-us" class="why-us">
        <h3>Pourquoi choisir SawbLi ?</h3>
        <div class="reasons">
            <div class="reason-box">
                <h4>Facilité d'utilisation</h4>
                <p>Notre interface est simple et conviviale.</p>
            </div>
            <div class="reason-box">
                <h4>Assistance continue</h4>
                <p>Assistance technique 24h/24 et 7j/7 disponible pour tous les membres.</p>
            </div>
            <div class="reason-box">
                <h4>Haute fiabilité</h4>
                <p>Systèmes de sécurité pour assurer des transactions sécurisées.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer">
            <div class="logo">SawbLi<span class="dot">.</span></div>
            <div class="social-media">
                <a href="#"><i class="ri-facebook-box-fill"></i></a>
                <a href="#"><i class="ri-twitter-fill"></i></a>
                <a href="#"><i class="ri-instagram-fill"></i></a>
                <a href="#"><i class="ri-linkedin-box-fill"></i></a>
            </div>
            <p>© 2025 SawbLi - Tous droits réservés</p>
        </div>
    </footer>

    <script src="index.js"></script>
</body>
</html>

