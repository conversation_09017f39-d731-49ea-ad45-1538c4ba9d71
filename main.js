document.addEventListener('DOMContentLoaded', function() {
    // Modal handling for login and signup
    const showLoginBtn = document.getElementById('show-login-btn');
    const loginSection = document.getElementById('login');
    const closeLoginBtn = document.getElementById('close-login-btn');
    const showSignupBtn = document.getElementById('show-signup-btn');
    const signupSection = document.getElementById('signup');
    const closeSignupBtn = document.getElementById('close-signup-btn');
    const switchToSignup = document.getElementById('switch-to-signup');
    const switchToLogin = document.getElementById('switch-to-login');

    // Function to toggle modals
    function toggleSection(section, display) {
        section.style.display = display;
    }

    // Show/hide login modal
    if (showLoginBtn) {
        showLoginBtn.addEventListener('click', function() {
            toggleSection(loginSection, 'block');
        });
    }
    
    if (closeLoginBtn) {
        closeLoginBtn.addEventListener('click', function() {
            toggleSection(loginSection, 'none');
        });
    }

    // Show/hide signup modal
    if (showSignupBtn) {
        showSignupBtn.addEventListener('click', function() {
            toggleSection(signupSection, 'block');
        });
    }
    
    if (closeSignupBtn) {
        closeSignupBtn.addEventListener('click', function() {
            toggleSection(signupSection, 'none');
        });
    }

    // Switch between login and signup
    if (switchToSignup) {
        switchToSignup.addEventListener('click', function() {
            toggleSection(loginSection, 'none');
            toggleSection(signupSection, 'block');
        });
    }
    
    if (switchToLogin) {
        switchToLogin.addEventListener('click', function() {
            toggleSection(signupSection, 'none');
            toggleSection(loginSection, 'block');
        });
    }

    // Close modals with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            if (loginSection) toggleSection(loginSection, 'none');
            if (signupSection) toggleSection(signupSection, 'none');
        }
    });

    // Form validations
    const validateForm = (formId, fields) => {
        const form = document.getElementById(formId);
        if (!form) return;

        form.addEventListener('submit', function(event) {
            let isValid = true;

            fields.forEach(field => {
                const input = document.getElementById(field);
                if (!input) return;

                const value = input.value.trim();
                
                if (!value) {
                    isValid = false;
                    input.classList.add('error');
                    
                    // Add or update error message
                    let errorMsg = input.nextElementSibling;
                    if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                        errorMsg = document.createElement('div');
                        errorMsg.classList.add('error-message');
                        errorMsg.style.color = 'red';
                        errorMsg.style.fontSize = '12px';
                        errorMsg.style.marginTop = '5px';
                        input.parentNode.insertBefore(errorMsg, input.nextSibling);
                    }
                    errorMsg.textContent = 'Ce champ est requis';
                } else {
                    input.classList.remove('error');
                    const errorMsg = input.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('error-message')) {
                        errorMsg.remove();
                    }
                }
            });

            if (!isValid) {
                event.preventDefault();
            }
        });
    };

    // Apply validation to forms
    validateForm('login-form', ['login-email', 'login-password']);
    validateForm('signup-form', ['signup-name', 'signup-email', 'signup-password']);
    
    // Service scroll buttons
    const prevBtn = document.getElementById('prev');
    const nextBtn = document.getElementById('next');
    const serviceContainer = document.querySelector('.service-boxes');
    
    if (prevBtn && nextBtn && serviceContainer) {
        prevBtn.addEventListener('click', function() {
            serviceContainer.scrollBy({ left: -300, behavior: 'smooth' });
        });
        
        nextBtn.addEventListener('click', function() {
            serviceContainer.scrollBy({ left: 300, behavior: 'smooth' });
        });
    }
    
    // Flash messages auto-hide
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');
    
    if (errorMessage) {
        setTimeout(() => {
            errorMessage.style.display = 'none';
        }, 5000);
    }
    
    if (successMessage) {
        setTimeout(() => {
            successMessage.style.display = 'none';
        }, 5000);
    }
    
    // Service deletion confirmation
    const deleteButtons = document.querySelectorAll('.delete-service-btn');
    
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Êtes-vous sûr de vouloir supprimer ce service ?')) {
                    e.preventDefault();
                }
            });
        });
    }
    
    // Price formatting for service forms
    const priceInput = document.getElementById('price');
    
    if (priceInput) {
        priceInput.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value)) {
                this.value = value.toFixed(2);
            }
        });
    }
    
    // Category filter on service browse page
    const categoryFilter = document.getElementById('category-filter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            document.getElementById('filter-form').submit();
        });
    }
});
