// Récupérer les éléments
const showLoginBtn = document.getElementById('show-login-btn');
const loginSection = document.getElementById('login');
const closeLoginBtn = document.getElementById('close-login-btn');
const showSignupBtn = document.getElementById('show-signup-btn');
const signupSection = document.getElementById('signup');
const closeSignupBtn = document.getElementById('close-signup-btn');

// Fonction pour afficher/masquer une section
function toggleSection(button, section, closeBtn) {
    button.addEventListener('click', function () {
        section.style.display = 'block';
    });
    closeBtn.addEventListener('click', function () {
        section.style.display = 'none';
    });
}

toggleSection(showLoginBtn, loginSection, closeLoginBtn);
toggleSection(showSignupBtn, signupSection, closeSignupBtn);

// Cacher les sections avec la touche Escape
document.addEventListener("keydown", function (event) {
    if (event.key === "Escape") {
        loginSection.style.display = "none";
        signupSection.style.display = "none";
    }
});

// Fonction pour gérer les dropdowns
function toggleDropdown(buttonId, menuId) {
    document.getElementById(buttonId).addEventListener("click", function (event) {
        event.preventDefault();
        document.getElementById(menuId).classList.toggle("show");
    });

    document.addEventListener("click", function (event) {
        let dropdown = document.getElementById(menuId);
        let button = document.getElementById(buttonId);

        if (!button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.classList.remove("show");
        }
    });
}

toggleDropdown("language-btn", "language-menu");
toggleDropdown("discover-btn", "discover-menu");

// Vérification des formulaires avant soumission
function checkFormAndRedirect(formId, fields) {
    document.getElementById(formId).addEventListener("submit", function (event) {
        event.preventDefault();

        let allFilled = true;
        fields.forEach(field => {
            let input = document.getElementById(field);
            let errorMsg = input.nextElementSibling;

            if (!input.value.trim()) {
                allFilled = false;
                input.style.border = "2px solid red";
                if (!errorMsg || !errorMsg.classList.contains("error-msg")) {
                    let span = document.createElement("span");
                    span.classList.add("error-msg");
                    span.style.color = "red";
                    span.textContent = "Veuillez remplir ce champ";
                    input.parentNode.appendChild(span);
                }
            } else {
                input.style.border = "";
                if (errorMsg) errorMsg.remove();
            }
        });

        if (allFilled) {
            sessionStorage.setItem("user", document.getElementById(fields[0]).value);
            window.location.href = "dashboard.html";
        }
    });
}

// Vérification pour login et signup
checkFormAndRedirect("login-form", ["login-email", "login-password"]);
checkFormAndRedirect("signup-form", ["signup-name", "signup-email", "signup-password"]);
