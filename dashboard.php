<?php
session_start();
require_once 'db.php';
$conn = $pdo;
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Redirect to appropriate dashboard
if ($user['user_type'] === 'freelancer') {
    header("Location: freelancer_dashboard.php");
} else {
    header("Location: client_dashboard.php");
}
exit;
?>
