<?php
session_start();
require_once 'db.php';
$conn = $pdo;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    echo password_hash('admin123', PASSWORD_DEFAULT);

    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    if ($user && password_verify($password, $user['password']) && $user['user_type'] === 'admin') {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_type'] = 'admin';
        header("Location: admin_dashboard.php");
        exit;
    } else {
        $error = "Email ou mot de passe incorrect, ou vous n'avez pas les droits administrateur.";
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Admin - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white p-8 rounded shadow-md w-full max-w-md">
            <h2 class="text-2xl font-bold mb-6 text-center">Connexion Admin</h2>
            <?php if (!empty($error)): ?>
                <div class="bg-red-100 text-red-700 p-3 rounded mb-4">
                    <?= $error ?>
                </div>
            <?php endif; ?>
            <form method="POST" action="">
                <div class="mb-4">
                    <label class="block mb-1 text-sm font-medium">Email</label>
                    <input type="email" name="email" class="w-full px-3 py-2 border rounded" required>
                </div>
                <div class="mb-6">
                    <label class="block mb-1 text-sm font-medium">Mot de passe</label>
                    <input type="password" name="password" class="w-full px-3 py-2 border rounded" >
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition">
                    Se connecter
                </button>
            </form>
        </div>
    </div>
</body>
</html>
