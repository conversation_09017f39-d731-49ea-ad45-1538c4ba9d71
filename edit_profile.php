<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    $_SESSION['error'] = "Utilisateur introuvable.";
    header("Location: index.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $full_name = htmlspecialchars($_POST['full_name'] ?? '', ENT_QUOTES, 'UTF-8');
    $bio = htmlspecialchars($_POST['bio'] ?? '', ENT_QUOTES, 'UTF-8');
    $phone = htmlspecialchars($_POST['phone'] ?? '', ENT_QUOTES, 'UTF-8');
    $address = htmlspecialchars($_POST['address'] ?? '', ENT_QUOTES, 'UTF-8');

    $profile_picture = $user['profile_picture'];

    // حذف صورة الملف الشخصي إذا طلب المستخدم ذلك
    if (isset($_POST['remove_picture']) && $_POST['remove_picture'] == 'yes') {
        $profile_picture_path = 'uploads/profile_pictures/' . $profile_picture;
        if (file_exists($profile_picture_path)) {
            unlink($profile_picture_path); // حذف الملف
        }
        $profile_picture = ''; // مسح اسم الصورة في قاعدة البيانات
    }

    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
        $fileTmpPath = $_FILES['profile_picture']['tmp_name'];
        $fileName = $_FILES['profile_picture']['name'];
        $fileNameCmps = explode(".", $fileName);
        $fileExtension = strtolower(end($fileNameCmps));
        $allowedfileExtensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($fileExtension, $allowedfileExtensions)) {
            $newFileName = uniqid('profile_', true) . '.' . $fileExtension;
            $uploadFileDir = 'uploads/profile_pictures/';
            $dest_path = $uploadFileDir . $newFileName;

            if (!is_dir($uploadFileDir)) {
                mkdir($uploadFileDir, 0755, true);
            }

            if (move_uploaded_file($fileTmpPath, $dest_path)) {
                $profile_picture = $newFileName;
            } else {
                $error = "Échec du téléchargement de la photo.";
            }
        } else {
            $error = "Type de fichier non autorisé.";
        }
    }

    if (empty($full_name)) {
        $error = "Le nom complet est requis.";
    } else if (empty($error)) {
        try {
            $stmt = $conn->prepare("UPDATE users SET full_name = ?, bio = ?, phone = ?, address = ?, profile_picture = ? WHERE id = ?");
            $stmt->execute([$full_name, $bio, $phone, $address, $profile_picture, $user_id]);

            if (!empty($_POST['new_password']) && !empty($_POST['current_password'])) {
                if (password_verify($_POST['current_password'], $user['password'])) {
                    if (strlen($_POST['new_password']) < 6) {
                        $error = "Le nouveau mot de passe doit contenir au moins 6 caractères.";
                    } elseif ($_POST['new_password'] !== $_POST['confirm_password']) {
                        $error = "Les nouveaux mots de passe ne correspondent pas.";
                    } else {
                        $new_password_hash = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
                        $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                        $stmt->execute([$new_password_hash, $user_id]);
                        $success = "Profil et mot de passe mis à jour avec succès!";
                    }
                } else {
                    $error = "Le mot de passe actuel est incorrect.";
                }
            } else {
                $success = "Profil mis à jour avec succès!";
            }

            if (empty($error)) {
                $_SESSION['success'] = $success;
                header("Location: profile.php");
                exit;
            }
        } catch (PDOException $e) {
            $error = "Erreur lors de la mise à jour du profil: " . $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2>Modifier votre profil</h2>

    <?php if (!empty($error)): ?>
        <div class="error-message" style="background-color: #fee; color: #e53e3e; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $error; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="success-message" style="background-color: #f0fff4; color: #38a169; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $success; ?>
        </div>
    <?php endif; ?>

    <form method="POST" action="edit_profile.php" enctype="multipart/form-data">
        <div class="form-group">
            <label for="full_name">Nom complet</label>
            <input type="text" id="full_name" name="full_name" value="<?= htmlspecialchars($user['full_name']) ?>" required>
        </div>

        <div class="form-group">
            <label for="bio">Biographie</label>
            <textarea id="bio" name="bio" rows="6"><?= htmlspecialchars_decode($user['bio'] ?? '') ?></textarea>
        </div>

        <div class="form-grid">
            <div class="form-group">
                <label for="phone">Téléphone</label>
                <input type="text" id="phone" name="phone" value="<?= htmlspecialchars_decode($user['phone'] ?? '') ?>">
            </div>

            <div class="form-group">
                <label for="address">Adresse</label>
                <input type="text" id="address" name="address" value="<?= htmlspecialchars_decode($user['address'] ?? '') ?>">
            </div>
        </div>

        <div class="form-group">
            <label for="profile_picture">Photo de profil</label><br>
            <?php if (!empty($user['profile_picture'])): ?>
                <img src="uploads/profile_pictures/<?= htmlspecialchars($user['profile_picture']) ?>" width="100" style="border-radius: 10px; margin-bottom: 10px;"><br>
                <label for="remove_picture">
                    <input type="checkbox" name="remove_picture" value="yes"> Supprimer la photo
                </label>
            <?php endif; ?>
            <input type="file" name="profile_picture" id="profile_picture" accept="image/*">
        </div>

        <h3 class="mt-6 mb-4 text-xl font-bold">Changer de mot de passe</h3>
        <p class="mb-4 text-gray-600">Laissez vide si vous ne souhaitez pas changer votre mot de passe.</p>

        <div class="form-group">
            <label for="current_password">Mot de passe actuel</label>
            <input type="password" id="current_password" name="current_password">
        </div>

        <div class="form-grid">
            <div class="form-group">
                <label for="new_password">Nouveau mot de passe</label>
                <input type="password" id="new_password" name="new_password">
                <small class="text-gray-500">6 caractères minimum</small>
            </div>

            <div class="form-group">
                <label for="confirm_password">Confirmer le nouveau mot de passe</label>
                <input type="password" id="confirm_password" name="confirm_password">
            </div>
        </div>

        <div class="form-buttons">
            <a href="profile.php" class="btn btn-secondary">Annuler</a>
            <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
