<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in and is a freelancer
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Check if user is a freelancer
$stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user || $user['user_type'] !== 'freelancer') {
    $_SESSION['error'] = "Seuls les freelancers peuvent ajouter des services.";
    header("Location: dashboard.php");
    exit;
}

// Check subscription limits
$stmt = $conn->prepare("SELECT COUNT(*) as service_count FROM services WHERE user_id = ?");
$stmt->execute([$user_id]);
$service_count = $stmt->fetch()['service_count'];

// Check if user has a pro subscription
$stmt = $conn->prepare("
    SELECT s.plan_type 
    FROM subscriptions s 
    WHERE s.user_id = ? AND s.is_active = TRUE 
    AND (s.end_date IS NULL OR s.end_date > CURRENT_TIMESTAMP)
    ORDER BY s.plan_type DESC
    LIMIT 1
");
$stmt->execute([$user_id]);
$subscription = $stmt->fetch();

$is_pro = ($subscription && in_array($subscription['plan_type'], ['pro', 'premium']));

// Free accounts are limited to 3 services
if ($service_count >= 3 && !$is_pro) {
    $_SESSION['error'] = "Vous avez atteint la limite de services pour un compte gratuit. Passez à SawbLi Pro pour créer des services illimités.";
    header("Location: freelancer_dashboard.php#abonnement");
    exit;
}

// Process form submission
$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = sanitize($_POST['title'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $category = sanitize($_POST['category'] ?? '');
    
    if (empty($title)) {
        $error = "Le titre est requis.";
    } elseif (empty($description)) {
        $error = "La description est requise.";
    } elseif ($price <= 0) {
        $error = "Le prix doit être supérieur à 0.";
    } elseif (empty($category)) {
        $error = "La catégorie est requise.";
    } else {
        try {
            $stmt = $conn->prepare("INSERT INTO services (title, description, price, user_id, category) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$title, $description, $price, $user_id, $category]);
            
            $_SESSION['success'] = "Service ajouté avec succès!";
            header("Location: freelancer_dashboard.php");
            exit;
        } catch (PDOException $e) {
            $error = "Erreur lors de l'ajout du service: " . $e->getMessage();
        }
    }
}

// Get all categories
$categories = [];
$stmt = $conn->query("SELECT name FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

include 'includes/header.php';
?>

<div class="form-container">
    <h2>Ajouter un Service</h2>
    
    <?php if (!empty($error)): ?>
        <div class="error-message" style="background-color: #fee; color: #e53e3e; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success)): ?>
        <div class="success-message" style="background-color: #f0fff4; color: #38a169; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $success; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="add_service.php">
        <div class="form-group">
            <label for="title">Titre du service</label>
            <input type="text" id="title" name="title" value="<?= htmlspecialchars($_POST['title'] ?? '') ?>" required>
        </div>
        
        <div class="form-group">
            <label for="category">Catégorie</label>
            <select id="category" name="category" required>
                <option value="">Sélectionnez une catégorie</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= htmlspecialchars($category) ?>" <?= (isset($_POST['category']) && $_POST['category'] === $category) ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="6" required><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="price">Prix (DH)</label>
            <input type="number" id="price" name="price" step="0.01" value="<?= htmlspecialchars($_POST['price'] ?? '') ?>" required>
        </div>
        
        <div class="form-buttons">
            <a href="freelancer_dashboard.php" class="btn btn-secondary">Annuler</a>
            <button type="submit" class="btn btn-primary">Ajouter le Service</button>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
