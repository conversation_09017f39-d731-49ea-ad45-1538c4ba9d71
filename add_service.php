<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in and is a freelancer
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Check if user is a freelancer
$stmt = $conn->prepare("SELECT user_type FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user || $user['user_type'] !== 'freelancer') {
    $_SESSION['error'] = "Seuls les freelancers peuvent ajouter des services.";
    header("Location: dashboard.php");
    exit;
}

// Check subscription limits
$stmt = $conn->prepare("SELECT COUNT(*) as service_count FROM services WHERE user_id = ?");
$stmt->execute([$user_id]);
$service_count = $stmt->fetch()['service_count'];

// Check if user has a pro subscription
$stmt = $conn->prepare("
    SELECT s.plan_type 
    FROM subscriptions s 
    WHERE s.user_id = ? AND s.is_active = TRUE 
    AND (s.end_date IS NULL OR s.end_date > CURRENT_TIMESTAMP)
    ORDER BY s.plan_type DESC
    LIMIT 1
");
$stmt->execute([$user_id]);
$subscription = $stmt->fetch();

$is_pro = ($subscription && in_array($subscription['plan_type'], ['pro', 'premium']));

// Free accounts are limited to 3 services
if ($service_count >= 3 && !$is_pro) {
    $_SESSION['error'] = "Vous avez atteint la limite de services pour un compte gratuit. Passez à SawbLi Pro pour créer des services illimités.";
    header("Location: freelancer_dashboard.php#abonnement");
    exit;
}

// Process form submission
$error = '';
$success = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = sanitize($_POST['title'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $category = sanitize($_POST['category'] ?? '');
    
    if (empty($title)) {
        $error = "Le titre est requis.";
    } elseif (empty($description)) {
        $error = "La description est requise.";
    } elseif ($price <= 0) {
        $error = "Le prix doit être supérieur à 0.";
    } elseif (empty($category)) {
        $error = "La catégorie est requise.";
    } else {
        try {
            $stmt = $conn->prepare("INSERT INTO services (title, description, price, user_id, category) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$title, $description, $price, $user_id, $category]);
            
            $_SESSION['success'] = "Service ajouté avec succès!";
            header("Location: freelancer_dashboard.php");
            exit;
        } catch (PDOException $e) {
            $error = "Erreur lors de l'ajout du service: " . $e->getMessage();
        }
    }
}

// Get all categories
$categories = [];
$stmt = $conn->query("SELECT name FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

include 'includes/header.php';
?>

<style>
/* Professional Service Creation Page Styles */
.service-creation-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-header-content {
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

.service-form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.form-progress {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    height: 4px;
    width: 100%;
}

.form-content {
    padding: 3rem;
}

.form-section {
    margin-bottom: 2.5rem;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #667eea;
    font-size: 1.2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.form-grid.two-columns {
    grid-template-columns: 1fr 1fr;
}

.form-group {
    position: relative;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.price-input-wrapper {
    position: relative;
}

.price-input-wrapper::before {
    content: 'DH';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-weight: 600;
    pointer-events: none;
}

.form-help {
    font-size: 0.85rem;
    color: #718096;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.form-help i {
    color: #667eea;
}

.character-count {
    position: absolute;
    bottom: 0.5rem;
    right: 1rem;
    font-size: 0.8rem;
    color: #a0aec0;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
    margin-top: 2rem;
}

.btn {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.alert-error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.alert-success {
    background: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.alert i {
    font-size: 1.2rem;
}

.service-preview {
    background: #f8fafc;
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin-top: 1rem;
}

.preview-placeholder {
    color: #a0aec0;
    font-style: italic;
}

.tips-card {
    background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
    border: 1px solid #9ae6b4;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.tips-title {
    font-weight: 600;
    color: #2f855a;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tips-list {
    list-style: none;
    padding: 0;
}

.tips-list li {
    color: #2f855a;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.tips-list li::before {
    content: '✓';
    color: #38a169;
    font-weight: bold;
    margin-top: 0.1rem;
}

@media (max-width: 768px) {
    .service-creation-container {
        padding: 0 0.5rem;
        margin: 1rem auto;
    }

    .page-title {
        font-size: 2rem;
    }

    .form-content {
        padding: 2rem 1.5rem;
    }

    .form-grid.two-columns {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}
</style>

<div class="service-creation-container">
    <!-- Professional Header -->
    <div class="page-header">
        <div class="page-header-content">
            <h1 class="page-title">
                <i class="ri-add-circle-line"></i>
                Créer un Nouveau Service
            </h1>
            <p class="page-subtitle">
                Présentez vos compétences et attirez de nouveaux clients
            </p>
        </div>
    </div>

    <!-- Service Creation Form -->
    <div class="service-form-card">
        <div class="form-progress"></div>

        <div class="form-content">
            <!-- Error/Success Messages -->
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <i class="ri-error-warning-line"></i>
                    <?= $error; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <i class="ri-checkbox-circle-line"></i>
                    <?= $success; ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="add_service.php" id="service-form">
                <!-- Basic Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="ri-information-line"></i>
                        Informations de Base
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="title" class="form-label">Titre du Service *</label>
                            <input
                                type="text"
                                id="title"
                                name="title"
                                class="form-input"
                                value="<?= htmlspecialchars($_POST['title'] ?? '') ?>"
                                placeholder="Ex: Je vais créer un site web professionnel"
                                maxlength="80"
                                required
                            >
                            <div class="form-help">
                                <i class="ri-lightbulb-line"></i>
                                Soyez précis et attractif. Commencez par "Je vais..."
                            </div>
                            <div class="character-count" id="title-count">0/80</div>
                        </div>
                    </div>

                    <div class="form-grid two-columns">
                        <div class="form-group">
                            <label for="category" class="form-label">Catégorie *</label>
                            <select id="category" name="category" class="form-select" required>
                                <option value="">Choisissez une catégorie</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category) ?>" <?= (isset($_POST['category']) && $_POST['category'] === $category) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-help">
                                <i class="ri-folder-line"></i>
                                Choisissez la catégorie qui correspond le mieux
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="price" class="form-label">Prix de Base *</label>
                            <div class="price-input-wrapper">
                                <input
                                    type="number"
                                    id="price"
                                    name="price"
                                    class="form-input"
                                    step="0.01"
                                    min="5"
                                    value="<?= htmlspecialchars($_POST['price'] ?? '') ?>"
                                    placeholder="100"
                                    required
                                >
                            </div>
                            <div class="form-help">
                                <i class="ri-money-dollar-circle-line"></i>
                                Prix minimum: 5 DH
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="ri-file-text-line"></i>
                        Description Détaillée
                    </h3>

                    <div class="form-group">
                        <label for="description" class="form-label">Description du Service *</label>
                        <textarea
                            id="description"
                            name="description"
                            class="form-textarea"
                            placeholder="Décrivez en détail ce que vous proposez, votre processus de travail, ce qui est inclus..."
                            maxlength="1000"
                            required
                        ><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        <div class="form-help">
                            <i class="ri-edit-line"></i>
                            Expliquez clairement ce que vous offrez et pourquoi choisir vos services
                        </div>
                        <div class="character-count" id="description-count">0/1000</div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="freelancer_dashboard.php" class="btn btn-secondary">
                        <i class="ri-arrow-left-line"></i>
                        Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ri-add-line"></i>
                        Créer le Service
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Professional Tips -->
    <div class="tips-card">
        <h4 class="tips-title">
            <i class="ri-lightbulb-flash-line"></i>
            Conseils pour un Service Réussi
        </h4>
        <ul class="tips-list">
            <li>Utilisez un titre clair et accrocheur qui décrit exactement ce que vous offrez</li>
            <li>Rédigez une description détaillée avec des exemples concrets</li>
            <li>Fixez un prix compétitif en étudiant la concurrence</li>
            <li>Soyez précis sur les délais de livraison</li>
            <li>Mentionnez vos qualifications et expériences pertinentes</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counters
    const titleInput = document.getElementById('title');
    const titleCount = document.getElementById('title-count');
    const descriptionInput = document.getElementById('description');
    const descriptionCount = document.getElementById('description-count');

    function updateCharacterCount(input, counter, max) {
        const current = input.value.length;
        counter.textContent = `${current}/${max}`;

        if (current > max * 0.9) {
            counter.style.color = '#e53e3e';
        } else if (current > max * 0.7) {
            counter.style.color = '#d69e2e';
        } else {
            counter.style.color = '#a0aec0';
        }
    }

    titleInput?.addEventListener('input', () => updateCharacterCount(titleInput, titleCount, 80));
    descriptionInput?.addEventListener('input', () => updateCharacterCount(descriptionInput, descriptionCount, 1000));

    // Initialize counters
    if (titleInput) updateCharacterCount(titleInput, titleCount, 80);
    if (descriptionInput) updateCharacterCount(descriptionInput, descriptionCount, 1000);

    // Form validation
    const form = document.getElementById('service-form');
    form?.addEventListener('submit', function(e) {
        const title = titleInput.value.trim();
        const description = descriptionInput.value.trim();
        const price = document.getElementById('price').value;

        if (title.length < 10) {
            e.preventDefault();
            alert('Le titre doit contenir au moins 10 caractères.');
            titleInput.focus();
            return;
        }

        if (description.length < 50) {
            e.preventDefault();
            alert('La description doit contenir au moins 50 caractères.');
            descriptionInput.focus();
            return;
        }

        if (parseFloat(price) < 5) {
            e.preventDefault();
            alert('Le prix minimum est de 5 DH.');
            document.getElementById('price').focus();
            return;
        }
    });

    // Auto-save draft (optional enhancement)
    let saveTimeout;
    function saveDraft() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
            const formData = {
                title: titleInput.value,
                description: descriptionInput.value,
                price: document.getElementById('price').value,
                category: document.getElementById('category').value
            };
            localStorage.setItem('service_draft', JSON.stringify(formData));
        }, 1000);
    }

    // Load draft on page load
    const savedDraft = localStorage.getItem('service_draft');
    if (savedDraft && !titleInput.value) {
        const draft = JSON.parse(savedDraft);
        if (draft.title) titleInput.value = draft.title;
        if (draft.description) descriptionInput.value = draft.description;
        if (draft.price) document.getElementById('price').value = draft.price;
        if (draft.category) document.getElementById('category').value = draft.category;

        // Update character counts
        updateCharacterCount(titleInput, titleCount, 80);
        updateCharacterCount(descriptionInput, descriptionCount, 1000);
    }

    // Save draft on input
    [titleInput, descriptionInput, document.getElementById('price'), document.getElementById('category')].forEach(input => {
        input?.addEventListener('input', saveDraft);
    });

    // Clear draft on successful submission
    form?.addEventListener('submit', () => {
        localStorage.removeItem('service_draft');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
