<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: dashboard.php");
    exit;
}

$error = '';
$user_type = $_GET['type'] ?? '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $username = sanitize($_POST['username'] ?? '');
    $full_name = sanitize($_POST['full_name'] ?? '');
    $user_type = $_POST['user_type'] ?? ''; // client or freelancer

    if (!empty($email) && !empty($password) && !empty($confirm_password) && !empty($username) && !empty($full_name) && !empty($user_type)) {
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Format d'email invalide.";
        } 
        // Check password length
        elseif (strlen($password) < 6) {
            $error = "Le mot de passe doit contenir au moins 6 caractères.";
        }
        // Check if passwords match
        elseif ($password !== $confirm_password) {
            $error = "Les mots de passe ne correspondent pas.";
        } 
        else {
            // Check if email already exists
            $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $error = "Cet email est déjà utilisé.";
            } else {
                // Check if username already exists
                $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
                $stmt->execute([$username]);
                
                if ($stmt->rowCount() > 0) {
                    $error = "Ce nom d'utilisateur est déjà utilisé.";
                } else {
                    // Hash the password securely
                    $password_hashed = password_hash($password, PASSWORD_DEFAULT);

                    // Insert new user into the database
                    $stmt = $conn->prepare("INSERT INTO users (username, email, password, user_type, full_name) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$username, $email, $password_hashed, $user_type, $full_name]);

                    $user_id = $conn->lastInsertId();
                    
                    // Create a free subscription
                    $stmt = $conn->prepare("INSERT INTO subscriptions (user_id, plan_type, is_active) VALUES (?, 'free', 1)");
                    $stmt->execute([$user_id]);

                    $_SESSION['user_id'] = $user_id;
                    $_SESSION['email'] = $email;
                    $_SESSION['user_type'] = $user_type;
                    $_SESSION['username'] = $username;
                    
                    $_SESSION['success'] = "Compte créé avec succès! Bienvenue sur SawbLi.";
                    
                    // Redirect based on user type
                    if ($user_type === 'freelancer') {
                        header("Location: freelancer_dashboard.php");
                    } else {
                        header("Location: client_dashboard.php");
                    }
                    exit;
                }
            }
        }
    } else {
        $error = "Tous les champs sont requis.";
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2><?= !empty($user_type) ? ($user_type === 'freelancer' ? "Devenir Freelancer" : "Créer un compte Client") : "Créer un compte" ?></h2>
    
    <?php if (!empty($error)): ?>
        <div class="error-message" style="background-color: #fee; color: #e53e3e; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $error; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="signup.php">
        <div class="form-grid">
            <div class="form-group">
                <label for="username">Nom d'utilisateur</label>
                <input type="text" id="username" name="username" value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" required>
            </div>
            
            <div class="form-group">
                <label for="full_name">Nom complet</label>
                <input type="text" id="full_name" name="full_name" value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
        </div>
        
        <div class="form-grid">
            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password" id="password" name="password" required>
                <small style="color: #666;">6 caractères minimum</small>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">Confirmer le mot de passe</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="user_type">Type de compte</label>
            <select id="user_type" name="user_type" required>
                <option value="">Choisissez un type</option>
                <option value="client" <?= (($user_type === 'client') ? 'selected' : '') ?>>Client</option>
                <option value="freelancer" <?= (($user_type === 'freelancer') ? 'selected' : '') ?>>Freelancer</option>
            </select>
        </div>
        
        <div class="form-buttons">
            <button type="submit" class="btn btn-primary">Créer un compte</button>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <p>Vous avez déjà un compte ? <a href="login.php">Se connecter</a></p>
        </div>

    </form>
</div>

<?php include 'includes/footer.php'; ?>
