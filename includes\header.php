<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'db.php';
$conn = $pdo;

// Get current user if logged in
$currentUser = null;
if (isset($_SESSION['user_id'])) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $currentUser = $stmt->fetch();
}

// Get flash messages
$errorMessage = $_SESSION['error'] ?? null;
$successMessage = $_SESSION['success'] ?? null;

// Clear flash messages
unset($_SESSION['error']);
unset($_SESSION['success']);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SawbLi - Plateforme de Freelance Marocaine</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />
</head>
<body>
    <!-- Flash Messages -->
    <?php if ($errorMessage): ?>
        <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?= $errorMessage; ?></span>
            <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg onclick="this.parentElement.parentElement.style.display='none'" class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Fermer</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </span>
        </div>
    <?php endif; ?>

    <?php if ($successMessage): ?>
        <div id="success-message" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?= $successMessage; ?></span>
            <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg onclick="this.parentElement.parentElement.style.display='none'" class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Fermer</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </span>
        </div>
    <?php endif; ?>

    <!-- Brand New Modern Navigation Bar -->
    <nav class="modern-navbar">
        <div class="navbar-container">
            <!-- Logo Section -->
            <div class="navbar-brand">
                <a href="index.php" class="brand-link">
                    <span class="brand-text">SawbLi</span>
                    <span class="brand-tagline">Freelance Platform</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="navbar-menu">
                <a href="index.php" class="nav-item">
                    <i class="ri-home-4-line"></i>
                    <span>Accueil</span>
                </a>
                <a href="browse_services.php" class="nav-item">
                    <i class="ri-service-line"></i>
                    <span>Services</span>
                </a>
                <?php if ($currentUser): ?>
                    <a href="<?= $currentUser['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php' ?>" class="nav-item">
                        <i class="ri-dashboard-3-line"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="received_messages.php" class="nav-item">
                        <i class="ri-notification-3-line"></i>
                        <span>Messages</span>
                        <?php
                        $stmt = $conn->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
                        $stmt->execute([$currentUser['id']]);
                        $unreadCount = $stmt->fetchColumn();
                        if ($unreadCount > 0): ?>
                            <span class="notification-badge"><?= $unreadCount ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="profile.php" class="nav-item">
                        <i class="ri-user-line"></i>
                        <span>Profil</span>
                    </a>
                    <a href="settings.php" class="nav-item">
                        <i class="ri-settings-3-line"></i>
                        <span>Paramètres</span>
                    </a>
                <?php endif; ?>
            </div>

            <!-- Search Bar -->
            <div class="navbar-search">
                <form action="browse_services.php" method="GET" class="search-form">
                    <div class="search-input-wrapper">
                        <i class="ri-search-line search-icon"></i>
                        <input type="text" name="search" placeholder="Rechercher des services..." class="search-input">
                        <button type="submit" class="search-btn">
                            <i class="ri-arrow-right-line"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- User Section -->
            <div class="navbar-user">
                <?php if ($currentUser): ?>
                    <div class="user-menu">
                        <button class="user-btn" id="user-menu-btn">
                            <div class="user-avatar">
                                <?php if ($currentUser['profile_picture']): ?>
                                    <img src="<?= htmlspecialchars($currentUser['profile_picture']) ?>" alt="Profile">
                                <?php else: ?>
                                    <span><?= strtoupper(substr($currentUser['full_name'] ?? $currentUser['username'], 0, 1)) ?></span>
                                <?php endif; ?>
                            </div>
                            <span class="user-name"><?= htmlspecialchars($currentUser['full_name'] ?? $currentUser['username']) ?></span>
                            <i class="ri-arrow-down-s-line"></i>
                        </button>
                        <div class="user-dropdown" id="user-dropdown">
                            <a href="profile.php" class="dropdown-item">
                                <i class="ri-user-line"></i>
                                <span>Mon Profil</span>
                            </a>
                            <a href="settings.php" class="dropdown-item">
                                <i class="ri-settings-3-line"></i>
                                <span>Paramètres</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="logout.php" class="dropdown-item logout">
                                <i class="ri-logout-box-line"></i>
                                <span>Déconnexion</span>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <button class="login-btn" id="login-btn">
                            <i class="ri-login-box-line"></i>
                            <span>Connexion</span>
                        </button>
                        <a href="signup.php" class="signup-btn">
                            <i class="ri-user-add-line"></i>
                            <span>S'inscrire</span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-toggle" id="mobile-toggle">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobile-menu">
            <div class="mobile-menu-header">
                <span class="mobile-brand">SawbLi</span>
                <button class="mobile-close" id="mobile-close">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="mobile-menu-content">
                <a href="index.php" class="mobile-nav-item">
                    <i class="ri-home-4-line"></i>
                    <span>Accueil</span>
                </a>
                <a href="browse_services.php" class="mobile-nav-item">
                    <i class="ri-service-line"></i>
                    <span>Services</span>
                </a>
                <?php if ($currentUser): ?>
                    <a href="<?= $currentUser['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php' ?>" class="mobile-nav-item">
                        <i class="ri-dashboard-3-line"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="received_messages.php" class="mobile-nav-item">
                        <i class="ri-notification-3-line"></i>
                        <span>Messages</span>
                        <?php if ($unreadCount > 0): ?>
                            <span class="notification-badge"><?= $unreadCount ?></span>
                        <?php endif; ?>
                    </a>
                    <div class="mobile-divider"></div>
                    <a href="profile.php" class="mobile-nav-item">
                        <i class="ri-user-line"></i>
                        <span>Mon Profil</span>
                    </a>
                    <a href="settings.php" class="mobile-nav-item">
                        <i class="ri-settings-3-line"></i>
                        <span>Paramètres</span>
                    </a>
                    <a href="logout.php" class="mobile-nav-item logout">
                        <i class="ri-logout-box-line"></i>
                        <span>Déconnexion</span>
                    </a>
                <?php else: ?>
                    <div class="mobile-divider"></div>
                    <button class="mobile-nav-item" id="mobile-login-btn">
                        <i class="ri-login-box-line"></i>
                        <span>Connexion</span>
                    </button>
                    <a href="signup.php" class="mobile-nav-item primary">
                        <i class="ri-user-add-line"></i>
                        <span>S'inscrire</span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Login Modal -->
    <div id="login-modal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h2>Connexion</h2>
                <button class="modal-close" id="close-login">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="login-form" method="POST" action="login.php">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Mot de passe</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Se connecter</button>
                </form>
            </div>
        </div>
    </div>

    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            padding-top: 70px;
        }

        /* Modern Navigation Bar */
        .modern-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        /* Brand/Logo */
        .navbar-brand .brand-link {
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
            line-height: 1;
        }

        .brand-tagline {
            font-size: 0.7rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 2px;
        }

        /* Desktop Navigation */
        .navbar-menu {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            text-decoration: none;
            color: #4b5563;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 500;
            position: relative;
        }

        .nav-item:hover {
            background: #f3f4f6;
            color: #2563eb;
        }

        .nav-item i {
            font-size: 1.1rem;
        }

        .notification-badge {
            position: absolute;
            top: 0.2rem;
            right: 0.2rem;
            background: #ef4444;
            color: white;
            font-size: 0.7rem;
            padding: 0.1rem 0.3rem;
            border-radius: 10px;
            min-width: 1.2rem;
            text-align: center;
        }

        /* Search Bar */
        .navbar-search {
            flex: 1;
            max-width: 400px;
            margin: 0 2rem;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .search-input-wrapper:focus-within {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            padding: 0 1rem;
            color: #6b7280;
            font-size: 1.1rem;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 0.75rem 0;
            font-size: 0.9rem;
            outline: none;
            color: #1f2937;
        }

        .search-input::placeholder {
            color: #9ca3af;
        }

        .search-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .search-btn:hover {
            background: #1d4ed8;
        }

        /* User Section */
        .navbar-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-menu {
            position: relative;
        }

        .user-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .user-btn:hover {
            background: #f3f4f6;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            z-index: 1001;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #4b5563;
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f3f4f6;
        }

        .dropdown-item.logout {
            color: #ef4444;
        }

        .dropdown-divider {
            height: 1px;
            background: #e5e7eb;
            margin: 0.5rem 0;
        }

        /* Auth Buttons */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .login-btn, .signup-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .login-btn {
            background: transparent;
            color: #4b5563;
        }

        .login-btn:hover {
            background: #f3f4f6;
            color: #2563eb;
        }

        .signup-btn {
            background: #2563eb;
            color: white;
        }

        .signup-btn:hover {
            background: #1d4ed8;
        }

        /* Mobile Toggle */
        .mobile-toggle {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
        }

        .hamburger-line {
            width: 24px;
            height: 3px;
            background: #2563eb;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 300px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1001;
            overflow-y: auto;
        }

        .mobile-menu.show {
            right: 0;
        }

        .mobile-menu-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .mobile-brand {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2563eb;
        }

        .mobile-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .mobile-menu-content {
            padding: 1rem;
        }

        .mobile-nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            text-decoration: none;
            color: #4b5563;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: background 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .mobile-nav-item:hover {
            background: #f3f4f6;
        }

        .mobile-nav-item.primary {
            background: #2563eb;
            color: white;
        }

        .mobile-nav-item.logout {
            color: #ef4444;
        }

        .mobile-divider {
            height: 1px;
            background: #e5e7eb;
            margin: 1rem 0;
        }

        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-container {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 400px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .modal-content {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .btn-primary {
            width: 100%;
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-menu, .navbar-search, .navbar-user {
                display: none;
            }

            .mobile-toggle {
                display: flex;
            }

            body {
                padding-top: 60px;
            }

            .navbar-container {
                height: 60px;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileToggle = document.getElementById('mobile-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileClose = document.getElementById('mobile-close');

            mobileToggle?.addEventListener('click', () => {
                mobileMenu.classList.add('show');
            });

            mobileClose?.addEventListener('click', () => {
                mobileMenu.classList.remove('show');
            });

            // User dropdown
            const userMenuBtn = document.getElementById('user-menu-btn');
            const userDropdown = document.getElementById('user-dropdown');

            userMenuBtn?.addEventListener('click', () => {
                userDropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!userMenuBtn?.contains(e.target)) {
                    userDropdown?.classList.remove('show');
                }
            });

            // Login modal
            const loginBtn = document.getElementById('login-btn');
            const mobileLoginBtn = document.getElementById('mobile-login-btn');
            const loginModal = document.getElementById('login-modal');
            const closeLogin = document.getElementById('close-login');

            loginBtn?.addEventListener('click', () => {
                loginModal.style.display = 'flex';
            });

            mobileLoginBtn?.addEventListener('click', () => {
                loginModal.style.display = 'flex';
                mobileMenu.classList.remove('show');
            });

            closeLogin?.addEventListener('click', () => {
                loginModal.style.display = 'none';
            });

            // Close modal when clicking overlay
            loginModal?.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    loginModal.style.display = 'none';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileToggle?.contains(e.target) && !mobileMenu?.contains(e.target)) {
                    mobileMenu?.classList.remove('show');
                }
            });
        });
    </script>
