<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'db.php';
$conn = $pdo;

// Get current user if logged in
$currentUser = null;
if (isset($_SESSION['user_id'])) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $currentUser = $stmt->fetch();
}

// Get flash messages
$errorMessage = $_SESSION['error'] ?? null;
$successMessage = $_SESSION['success'] ?? null;

// Clear flash messages
unset($_SESSION['error']);
unset($_SESSION['success']);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SawbLi - Plateforme de Freelance Marocaine</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />
</head>
<body>
    <!-- Flash Messages -->
    <?php if ($errorMessage): ?>
        <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?= $errorMessage; ?></span>
            <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg onclick="this.parentElement.parentElement.style.display='none'" class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Fermer</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </span>
        </div>
    <?php endif; ?>

    <?php if ($successMessage): ?>
        <div id="success-message" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?= $successMessage; ?></span>
            <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg onclick="this.parentElement.parentElement.style.display='none'" class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Fermer</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </span>
        </div>
    <?php endif; ?>

    <!-- Professional Header -->
    <header class="professional-header">
        <div class="header-wrapper">
            <!-- Top Bar (Optional - for contact info, social links) -->
            <div class="header-top-bar">
                <div class="container">
                    <div class="top-bar-content">
                        <div class="contact-info">
                            <span><i class="ri-phone-line"></i> +212 770 876 664</span>
                            <span><i class="ri-mail-line"></i> <EMAIL></span>
                        </div>
                        <div class="social-links">
                            <a href="#" aria-label="Facebook"><i class="ri-facebook-line"></i></a>
                            <a href="#" aria-label="LinkedIn"><i class="ri-linkedin-line"></i></a>
                            <a href="#" aria-label="Twitter"><i class="ri-twitter-line"></i></a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Header -->
            <div class="header-main">
                <div class="container">
                    <div class="header-content">
                        <!-- Logo Section -->
                        <div class="logo-section">
                            <a href="index.php" class="logo-link">
                                <div class="logo-container">
                                    <div class="logo-text">
                                        <span class="logo-main">SawbLi</span>
                                        <span class="logo-tagline">Freelance Platform</span>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <!-- Advanced Search Bar -->
                        <div class="search-section">
                            <form action="search.php" method="GET" class="advanced-search-form">
                                <div class="search-input-group">
                                    <div class="search-icon">
                                        <i class="ri-search-line"></i>
                                    </div>
                                    <input type="text"
                                           name="q"
                                           class="search-input"
                                           placeholder="Rechercher des services, freelances..."
                                           autocomplete="off">
                                    <div class="search-filters">
                                        <select name="category" class="search-category">
                                            <option value="">Toutes catégories</option>
                                            <option value="Développement">Développement</option>
                                            <option value="Marketing Digital">Marketing</option>
                                            <option value="Graphisme">Design</option>
                                            <option value="Rédaction">Rédaction</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="search-btn">
                                        <span>Rechercher</span>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Professional Navigation -->
                        <nav class="main-navigation">
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a href="index.php" class="nav-link">
                                        <i class="ri-home-4-line"></i>
                                        <span>Accueil</span>
                                    </a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a href="browse_services.php" class="nav-link">
                                        <i class="ri-service-line"></i>
                                        <span>Services</span>
                                        <i class="ri-arrow-down-s-line dropdown-arrow"></i>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a href="browse_services.php?category=Développement"><i class="ri-code-line"></i> Développement</a></li>
                                        <li><a href="browse_services.php?category=Marketing Digital"><i class="ri-megaphone-line"></i> Marketing Digital</a></li>
                                        <li><a href="browse_services.php?category=Graphisme"><i class="ri-palette-line"></i> Design & Graphisme</a></li>
                                        <li><a href="browse_services.php?category=Rédaction"><i class="ri-quill-pen-line"></i> Rédaction</a></li>
                                        <li class="divider"></li>
                                        <li><a href="browse_services.php"><i class="ri-grid-line"></i> Tous les services</a></li>
                                    </ul>
                                </li>

                                <?php if ($currentUser): ?>
                                    <li class="nav-item">
                                        <?php if ($currentUser['user_type'] === 'freelancer'): ?>
                                            <a href="freelancer_dashboard.php" class="nav-link">
                                                <i class="ri-dashboard-3-line"></i>
                                                <span>Dashboard</span>
                                            </a>
                                        <?php else: ?>
                                            <a href="client_dashboard.php" class="nav-link">
                                                <i class="ri-dashboard-3-line"></i>
                                                <span>Dashboard</span>
                                            </a>
                                        <?php endif; ?>
                                    </li>

                                    <!-- Professional Notifications -->
                                    <li class="nav-item">
                                        <a href="received_messages.php" class="nav-link notification-link">
                                            <i class="ri-notification-3-line"></i>
                                            <?php
                                                $stmt = $conn->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
                                                $stmt->execute([$currentUser['id']]);
                                                $unreadCount = $stmt->fetchColumn();
                                            ?>
                                            <?php if ($unreadCount > 0): ?>
                                                <span class="notification-badge"><?= $unreadCount ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>

                                    <!-- Professional User Menu -->
                                    <li class="nav-item dropdown user-dropdown">
                                        <a href="#" class="nav-link user-link" id="user-btn">
                                            <div class="user-avatar">
                                                <?php if ($currentUser['profile_picture']): ?>
                                                    <img src="<?= htmlspecialchars($currentUser['profile_picture']) ?>" alt="Profile">
                                                <?php else: ?>
                                                    <span class="avatar-initial"><?= strtoupper(substr($currentUser['full_name'] ?? $currentUser['username'], 0, 1)) ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="user-info">
                                                <span class="user-name"><?= htmlspecialchars($currentUser['username']) ?></span>
                                                <span class="user-type"><?= $currentUser['user_type'] === 'freelancer' ? 'Freelancer' : 'Client' ?></span>
                                            </div>
                                            <i class="ri-arrow-down-s-line dropdown-arrow"></i>
                                        </a>
                                        <ul class="dropdown-menu user-menu" id="user-menu">
                                            <li class="menu-header">
                                                <div class="user-details">
                                                    <strong><?= htmlspecialchars($currentUser['full_name'] ?? $currentUser['username']) ?></strong>
                                                    <small><?= htmlspecialchars($currentUser['email']) ?></small>
                                                </div>
                                            </li>
                                            <li class="divider"></li>
                                            <li><a href="profile.php"><i class="ri-user-line"></i> Mon Profil</a></li>
                                            <li><a href="edit_profile.php"><i class="ri-settings-3-line"></i> Paramètres</a></li>
                                            <?php if ($currentUser['user_type'] === 'freelancer'): ?>
                                                <li><a href="add_service.php"><i class="ri-add-circle-line"></i> Ajouter un Service</a></li>
                                                <li><a href="freelancer_dashboard.php"><i class="ri-bar-chart-line"></i> Mes Statistiques</a></li>
                                            <?php else: ?>
                                                <li><a href="client_dashboard.php"><i class="ri-file-list-3-line"></i> Mes Projets</a></li>
                                            <?php endif; ?>
                                            <li class="divider"></li>
                                            <li><a href="logout.php" class="logout-link"><i class="ri-logout-box-line"></i> Déconnexion</a></li>
                                        </ul>
                                    </li>
                                <?php else: ?>
                                    <li class="nav-item">
                                        <a href="#" id="show-login-btn" class="nav-link login-btn">
                                            <i class="ri-login-box-line"></i>
                                            <span>Connexion</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <button class="signup-btn" id="show-signup-btn">
                                            <i class="ri-user-add-line"></i>
                                            <span>S'inscrire</span>
                                        </button>
                                    </li>
                                <?php endif; ?>

                                <!-- Show "Devenir Freelancer" only for logged-in clients -->
                                <?php if ($currentUser && $currentUser['user_type'] === 'client'): ?>
                                    <li class="nav-item">
                                        <a href="signup.php?type=freelancer" class="nav-link freelancer-cta">
                                            <i class="ri-user-star-line"></i>
                                            <span>Devenir Freelancer</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <!-- Language Selector -->
                                <li class="nav-item dropdown language-selector">
                                    <a href="#" class="nav-link" id="language-btn">
                                        <i class="ri-global-line"></i>
                                        <span>FR</span>
                                        <i class="ri-arrow-down-s-line dropdown-arrow"></i>
                                    </a>
                                    <ul class="dropdown-menu language-menu" id="language-menu">
                                        <li><a href="?lang=fr"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHdpZHRoPSI2LjY3IiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDAyMzk1Ii8+CjxyZWN0IHg9IjEzLjMzIiB3aWR0aD0iNi42NyIgaGVpZ2h0PSIxNSIgZmlsbD0iI0VEMjkzOSIvPgo8L3N2Zz4K" alt="FR"> Français</a></li>
                                        <li><a href="?lang=ar"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjQzEyNzJEIi8+CjxwYXRoIGQ9Ik0xMCA3LjVMMTIuNSA1TDEwIDIuNUw3LjUgNUwxMCA3LjVaIiBmaWxsPSIjMDA2MjMzIi8+Cjwvc3ZnPgo=" alt="AR"> العربية</a></li>
                                        <li><a href="?lang=en"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+CjxwYXRoIGQ9Ik0wIDBoMjB2MUgwVjB6bTAgMmgyMHYxSDBWMnptMCAyaDIwdjFIMFY0em0wIDJoMjB2MUgwVjZ6bTAgMmgyMHYxSDBWOHptMCAyaDIwdjFIMFYxMHptMCAyaDIwdjFIMFYxMnptMCAyaDIwdjFIMFYxNHoiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3Qgd2lkdGg9IjgiIGhlaWdodD0iOCIgZmlsbD0iIzAxMjE2OSIvPgo8L3N2Zz4K" alt="EN"> English</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </nav>

                        <!-- Mobile Menu Toggle Button -->
                        <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menu" aria-expanded="false">
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Professional Mobile Navigation -->
        <div class="mobile-nav-overlay" id="mobile-nav">
            <div class="mobile-nav-container">
                <!-- Mobile Header -->
                <div class="mobile-nav-header">
                    <div class="mobile-logo">
                        <span>SawbLi</span>
                    </div>
                    <button class="mobile-nav-close" id="mobile-nav-close">
                        <i class="ri-close-line"></i>
                    </button>
                </div>

                <!-- Mobile Search -->
                <div class="mobile-search-section">
                    <form action="search.php" method="GET" class="mobile-search-form">
                        <div class="mobile-search-input">
                            <i class="ri-search-line"></i>
                            <input type="text" name="q" placeholder="Rechercher des services...">
                        </div>
                        <button type="submit" class="mobile-search-btn">
                            Rechercher
                        </button>
                    </form>
                </div>

                <!-- Mobile Menu Items -->
                <nav class="mobile-nav-menu">
                    <a href="index.php" class="mobile-nav-item">
                        <div class="nav-item-icon">
                            <i class="ri-home-4-line"></i>
                        </div>
                        <div class="nav-item-content">
                            <span class="nav-item-title">Accueil</span>
                            <span class="nav-item-desc">Page principale</span>
                        </div>
                        <i class="ri-arrow-right-s-line"></i>
                    </a>

                    <a href="browse_services.php" class="mobile-nav-item">
                        <div class="nav-item-icon">
                            <i class="ri-service-line"></i>
                        </div>
                        <div class="nav-item-content">
                            <span class="nav-item-title">Services</span>
                            <span class="nav-item-desc">Parcourir tous les services</span>
                        </div>
                        <i class="ri-arrow-right-s-line"></i>
                    </a>

                    <?php if ($currentUser): ?>
                        <?php if ($currentUser['user_type'] === 'freelancer'): ?>
                            <a href="freelancer_dashboard.php" class="mobile-nav-item">
                                <div class="nav-item-icon">
                                    <i class="ri-dashboard-3-line"></i>
                                </div>
                                <div class="nav-item-content">
                                    <span class="nav-item-title">Mon Dashboard</span>
                                    <span class="nav-item-desc">Gérer mes services</span>
                                </div>
                                <i class="ri-arrow-right-s-line"></i>
                            </a>

                            <a href="add_service.php" class="mobile-nav-item">
                                <div class="nav-item-icon">
                                    <i class="ri-add-circle-line"></i>
                                </div>
                                <div class="nav-item-content">
                                    <span class="nav-item-title">Ajouter un Service</span>
                                    <span class="nav-item-desc">Créer une nouvelle offre</span>
                                </div>
                                <i class="ri-arrow-right-s-line"></i>
                            </a>
                        <?php else: ?>
                            <a href="client_dashboard.php" class="mobile-nav-item">
                                <div class="nav-item-icon">
                                    <i class="ri-dashboard-3-line"></i>
                                </div>
                                <div class="nav-item-content">
                                    <span class="nav-item-title">Mon Dashboard</span>
                                    <span class="nav-item-desc">Mes projets et commandes</span>
                                </div>
                                <i class="ri-arrow-right-s-line"></i>
                            </a>
                        <?php endif; ?>

                        <a href="received_messages.php" class="mobile-nav-item">
                            <div class="nav-item-icon">
                                <i class="ri-notification-3-line"></i>
                                <?php if ($unreadCount > 0): ?>
                                    <span class="mobile-notification-badge"><?= $unreadCount ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">Messages</span>
                                <span class="nav-item-desc">
                                    <?= $unreadCount > 0 ? $unreadCount . ' nouveau(x) message(s)' : 'Aucun nouveau message' ?>
                                </span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>

                        <a href="profile.php" class="mobile-nav-item">
                            <div class="nav-item-icon">
                                <i class="ri-user-line"></i>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">Mon Profil</span>
                                <span class="nav-item-desc">Informations personnelles</span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>

                        <div class="mobile-nav-divider"></div>

                        <a href="logout.php" class="mobile-nav-item logout-item">
                            <div class="nav-item-icon">
                                <i class="ri-logout-box-line"></i>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">Déconnexion</span>
                                <span class="nav-item-desc">Se déconnecter du compte</span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>
                    <?php else: ?>
                        <a href="#" id="mobile-login-btn" class="mobile-nav-item">
                            <div class="nav-item-icon">
                                <i class="ri-login-box-line"></i>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">Se connecter</span>
                                <span class="nav-item-desc">Accéder à votre compte</span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>

                        <a href="#" id="mobile-signup-btn" class="mobile-nav-item primary-item">
                            <div class="nav-item-icon">
                                <i class="ri-user-add-line"></i>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">S'inscrire</span>
                                <span class="nav-item-desc">Créer un nouveau compte</span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Show "Devenir Freelancer" only for logged-in clients -->
                    <?php if ($currentUser && $currentUser['user_type'] === 'client'): ?>
                        <div class="mobile-nav-divider"></div>

                        <a href="signup.php?type=freelancer" class="mobile-nav-item featured-item">
                            <div class="nav-item-icon">
                                <i class="ri-user-star-line"></i>
                            </div>
                            <div class="nav-item-content">
                                <span class="nav-item-title">Devenir Freelancer</span>
                                <span class="nav-item-desc">Commencer à vendre vos services</span>
                            </div>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>
                    <?php endif; ?>
                </nav>

                <!-- Mobile Footer -->
                <div class="mobile-nav-footer">
                    <div class="mobile-contact">
                        <p><i class="ri-phone-line"></i> +212 6XX XXX XXX</p>
                        <p><i class="ri-mail-line"></i> <EMAIL></p>
                    </div>
                    <div class="mobile-social">
                        <a href="#"><i class="ri-facebook-line"></i></a>
                        <a href="#"><i class="ri-linkedin-line"></i></a>
                        <a href="#"><i class="ri-twitter-line"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Section modale de connexion -->
    <section id="login" class="login" style="display: none;">
        <div class="login-overlay">
            <!-- Bouton pour fermer la section -->
            <button id="close-login-btn" class="close-login-btn">x</button>
            <div class="login-container">
                <h2>Connectez-vous</h2>

                <!-- Error message container -->
                <div id="login-error" class="error-message" style="display: none;">
                    <i class="ri-error-warning-line"></i>
                    <span id="login-error-text"></span>
                </div>

                <!-- Success message container -->
                <div id="login-success" class="success-message" style="display: none;">
                    <i class="ri-check-line"></i>
                    <span id="login-success-text"></span>
                </div>

                <form id="login-form" method="POST">
                    <div class="form-group">
                        <label for="login-email">Email :</label>
                        <input type="email" name="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Mot de passe :</label>
                        <input type="password" name="password" id="login-password" required>
                    </div>
                    <button type="submit" class="login-btn" id="login-submit-btn">
                        <span class="btn-text">Connexion</span>
                        <span class="btn-loading" style="display: none;">
                            <i class="ri-loader-4-line"></i> Connexion...
                        </span>
                    </button>
                </form>

                <div class="forgot-password">
                    <a href="#">Mot de passe oublié ?</a>
                </div>
                <div class="signup-link">
                    <p>Vous n'avez pas de compte ? <a href="#" id="switch-to-signup">S'inscrire</a></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Section modale d'inscription -->
    <section id="signup" class="signup" style="display: none;">
        <div class="signup-overlay">
            <!-- Bouton pour fermer la section -->
            <button id="close-signup-btn" class="close-signup-btn">x</button>
            <div class="signup-container">
                <h2>Créer un compte</h2>
                <form id="signup-form" method="POST" action="signup.php">
                    <input type="text" name="username" id="signup-name" placeholder="Nom d'utilisateur" required>
                    <input type="text" name="full_name" placeholder="Nom complet" required>
                    <input type="email" name="email" id="signup-email" placeholder="Email" required>
                    <input type="password" name="password" id="signup-password" placeholder="Mot de passe" required>
                    <input type="password" name="confirm_password" placeholder="Confirmer le mot de passe" required>

                    <select name="user_type" required>
                        <option value="">Choisissez un type</option>
                        <option value="client">Client</option>
                        <option value="freelancer">Freelancer</option>
                    </select>

                    <button type="submit">Créer un compte</button>
                </form>

                <div class="login-link">
                    <p>Vous avez déjà un compte ? <a href="#" id="switch-to-login">Se connecter</a></p>
                </div>
            </div>
        </div>
    </section>
    
    <main>
        <!-- Your main content goes here -->
    </main>

<style>
/* ========================================
   PROFESSIONAL HEADER STYLES - GLOBAL
   ======================================== */

.professional-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
    background: #ffffff !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    display: block !important;
}

/* Hide any conflicting headers */
.header:not(.professional-header) {
    display: none !important;
}

/* Global body padding for all pages */
body {
    margin: 0 !important;
    padding-top: 60px !important;
}

/* ========================================
   RESPONSIVE HEADER - CONSOLIDATED
   ======================================== */

/* Base styles */
.professional-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
    background: #ffffff !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.header-wrapper {
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
}

/* Professional Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 1002;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    position: relative;
}

.mobile-menu-toggle:hover {
    background: rgba(37, 99, 235, 0.1);
    transform: scale(1.05);
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
}

.hamburger-line {
    width: 24px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

/* Professional Mobile Menu Toggle Animation */
.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu-toggle.active {
    background: rgba(37, 99, 235, 0.1);
}

/* Show mobile menu toggle on mobile devices */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex !important;
        order: 3;
        background: rgba(37, 99, 235, 0.1) !important; /* Temporary debug background */
        border: 2px solid #2563eb !important; /* Temporary debug border */
    }

    .main-navigation {
        display: none !important;
    }

    .search-section {
        display: none !important;
    }
}

/* Navigation transitions */
.main-navigation {
    transition: all 0.3s ease;
}

.nav-link span {
    transition: all 0.3s ease;
}

/* Top Bar */
.header-top-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    font-size: 0.7rem;
    padding: 2px 0;
    width: 100%;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.contact-info {
    display: flex;
    gap: 1rem;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.7rem;
}

.social-links {
    display: flex;
    gap: 0.5rem;
}

.social-links a {
    color: white;
    font-size: 0.8rem;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
    opacity: 0.8;
}

/* Main Header */
.header-main {
    background: white;
    padding: 0.3rem 0;
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Professional Logo */
.logo-section {
    flex-shrink: 0;
}

.logo-link {
    text-decoration: none;
    color: inherit;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-main {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.logo-tagline {
    font-size: 0.6rem;
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Advanced Search */
.search-section {
    flex: 1;
    max-width: 350px;
    width: 100%;
}

.advanced-search-form {
    position: relative;
}

.search-input-group {
    display: flex;
    align-items: center;
    background: #f8f9ff;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    padding: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    width: 100%;
}

.search-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 4px 20px rgba(19, 64, 116, 0.15);
}

.search-icon {
    padding: 0 0.5rem;
    color: var(--secondary-color);
    font-size: 0.85rem;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0.4rem 0;
    font-size: 0.8rem;
    outline: none;
    color: var(--text-color);
}

.search-input::placeholder {
    color: #999;
}

.search-category {
    border: none;
    background: transparent;
    padding: 0.3rem;
    color: var(--text-color);
    font-size: 0.7rem;
    outline: none;
    cursor: pointer;
}

.search-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    font-size: 0.75rem;
}

.search-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(19, 64, 116, 0.3);
}

/* Professional Navigation */
.main-navigation {
    flex-shrink: 0;
    display: none; /* Hidden by default, shown on desktop */
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.35rem 0.6rem;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.8rem;
    min-height: 32px;
}

.nav-link:hover {
    background: #f8f9ff;
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-link i {
    font-size: 0.8rem;
    flex-shrink: 0;
}

/* Dropdown Menus */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e1e5e9;
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.dropdown-menu a:hover {
    background: #f8f9ff;
    color: var(--primary-color);
}

.dropdown-menu .divider {
    height: 1px;
    background: #e1e5e9;
    margin: 0.5rem 0;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* User Menu Specific */
.user-dropdown .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 280px;
}

.user-link {
    background: #f8f9ff;
    border: 1px solid #e1e5e9;
    border-radius: 25px;
    padding: 0.3rem 0.6rem;
}

.user-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-initial {
    color: white;
    font-weight: 600;
    font-size: 0.6rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.65rem;
    line-height: 1.2;
}

.user-type {
    font-size: 0.5rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.2px;
}

.menu-header {
    padding: 1rem 1.25rem 0.5rem;
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 0.5rem;
}

.user-details strong {
    display: block;
    color: var(--text-color);
    font-size: 0.95rem;
}

.user-details small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.logout-link {
    color: #e53e3e !important;
}

.logout-link:hover {
    background: #fee !important;
}

/* Notification Badge */
.notification-link {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -3px;
    right: -3px;
    background: #e53e3e;
    color: white;
    font-size: 0.6rem;
    font-weight: 600;
    padding: 0.15rem 0.4rem;
    border-radius: 8px;
    min-width: 14px;
    text-align: center;
    line-height: 1;
}

/* CTA Buttons */
.freelancer-cta {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white !important;
    border-radius: 20px;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.freelancer-cta:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
}

.login-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white !important;
    border: none;
    border-radius: 20px;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.login-btn:hover {
    background: linear-gradient(135deg, var(--text-color) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(19, 64, 116, 0.3);
    color: white !important;
    text-decoration: none;
}

.signup-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(19, 64, 116, 0.3);
}

/* Language Selector */
.language-selector .nav-link {
    min-width: 60px;
    justify-content: center;
}

.language-menu {
    min-width: 160px;
}

.language-menu img {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: 2px;
}



/* ========================================
   PROFESSIONAL MOBILE NAVIGATION
   ======================================== */

/* SIMPLIFIED MOBILE NAVIGATION */
.mobile-nav-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.7) !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.mobile-nav-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.mobile-nav-container {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 300px !important;
    max-width: 85vw !important;
    height: 100vh !important;
    background: white !important;
    transform: translateX(100%) !important;
    transition: transform 0.3s ease !important;
    overflow-y: auto !important;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.2) !important;
    z-index: 10000 !important;
}

.mobile-nav-overlay.active .mobile-nav-container {
    transform: translateX(0) !important;
}

.mobile-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
}

.mobile-logo {
    display: flex;
    align-items: center;
    font-size: 1.3rem;
    font-weight: 700;
}

.mobile-nav-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background 0.2s ease;
}

.mobile-nav-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.mobile-search-section {
    padding: 1.5rem;
    background: #f8f9ff;
    border-bottom: 1px solid #e1e5e9;
}

.mobile-search-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-search-input {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 0.75rem;
    gap: 0.75rem;
}

.mobile-search-input i {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.mobile-search-input input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    color: var(--text-color);
}

.mobile-search-input input::placeholder {
    color: #999;
}

.mobile-search-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.mobile-search-btn:hover {
    transform: translateY(-1px);
}

.mobile-nav-menu {
    padding: 1rem 0;
}

.mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f5f5f5;
}

.mobile-nav-item:hover {
    background: #f8f9ff;
    color: var(--primary-color);
}

.nav-item-icon {
    position: relative;
    width: 40px;
    height: 40px;
    background: #f8f9ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.nav-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.nav-item-title {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.2;
}

.nav-item-desc {
    font-size: 0.8rem;
    color: var(--secondary-color);
    line-height: 1.3;
}

.mobile-nav-item i:last-child {
    color: var(--secondary-color);
    font-size: 1rem;
}

.mobile-notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e53e3e;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
}

.mobile-nav-divider {
    height: 1px;
    background: #e1e5e9;
    margin: 1rem 1.5rem;
}

.featured-item {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white !important;
    margin: 0.5rem 1rem;
    border-radius: 12px;
    border: none !important;
}

.featured-item .nav-item-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.featured-item .nav-item-desc {
    color: rgba(255, 255, 255, 0.8);
}

.featured-item i:last-child {
    color: white;
}

.primary-item {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-color) 100%);
    color: white !important;
    margin: 0.5rem 1rem;
    border-radius: 12px;
    border: none !important;
}

.primary-item .nav-item-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.primary-item .nav-item-desc {
    color: rgba(255, 255, 255, 0.8);
}

.primary-item i:last-child {
    color: white;
}

.logout-item:hover {
    background: #fee !important;
    color: #e53e3e !important;
}

.logout-item .nav-item-icon {
    background: #fee;
    color: #e53e3e;
}

.mobile-nav-footer {
    padding: 1.5rem;
    background: #f8f9ff;
    border-top: 1px solid #e1e5e9;
    margin-top: auto;
}

.mobile-contact {
    margin-bottom: 1rem;
}

.mobile-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.mobile-contact i {
    color: var(--primary-color);
}

.mobile-social {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.mobile-social a {
    width: 35px;
    height: 35px;
    background: var(--primary-color);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.1rem;
    transition: transform 0.2s ease;
}

.mobile-social a:hover {
    transform: translateY(-2px);
}

/* ========================================
   RESPONSIVE HEADER STYLES
   ======================================== */

/* ========================================
   RESPONSIVE BREAKPOINTS - CLEAN VERSION
   ======================================== */

/* Extra Large Desktop (1200px+) */
@media (min-width: 1200px) {
    body {
        padding-top: 70px !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
        max-width: 500px;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    .nav-menu {
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
        min-height: 34px;
    }

    .nav-link span {
        display: inline !important;
    }

    .signup-btn {
        padding: 0.45rem 0.9rem;
        font-size: 0.85rem;
    }

    .header-content,
    .top-bar-content {
        max-width: 1600px;
        margin: 0 auto;
        padding: 0 3rem;
    }
}

/* Large Desktop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    body {
        padding-top: 65px !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
        max-width: 400px;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    .nav-menu {
        gap: 0.4rem;
    }

    .nav-link {
        padding: 0.4rem 0.7rem;
        font-size: 0.8rem;
        min-height: 32px;
    }

    .nav-link span {
        display: inline !important;
    }

    .signup-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .header-content,
    .top-bar-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }
}

/* Tablet (769px - 991px) */
@media (min-width: 769px) and (max-width: 991px) {
    body {
        padding-top: 60px !important;
    }

    .header-top-bar {
        display: none !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: flex !important;
        max-width: 250px;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    .header-content {
        padding: 0 1.5rem;
        gap: 1rem;
    }

    .search-input {
        font-size: 0.8rem;
    }

    .nav-menu {
        gap: 0.2rem;
    }

    .nav-link {
        padding: 0.4rem;
        min-width: 32px;
        justify-content: center;
        font-size: 0.75rem;
        min-height: 32px;
    }

    .nav-link span {
        display: none !important;
    }

    .nav-link i {
        font-size: 0.9rem;
    }

    .user-info {
        display: none !important;
    }
}

/* Mobile (≤768px) */
@media (max-width: 768px) {
    body {
        padding-top: 50px !important;
    }

    .professional-header {
        padding-top: 0;
        width: 100%;
    }

    .header-top-bar {
        display: none !important;
    }

    .header-main {
        padding: 0.5rem 0;
        width: 100%;
    }

    .header-content {
        gap: 1rem;
        padding: 0 1rem;
        width: 100%;
        justify-content: space-between;
        display: flex !important;
        align-items: center !important;
    }

    .logo-section {
        flex-shrink: 0;
        order: 1;
    }

    .mobile-menu-toggle {
        order: 2;
        flex-shrink: 0;
        margin-left: auto;
    }

    .logo-main {
        font-size: 1.2rem;
    }

    .logo-tagline {
        font-size: 0.6rem;
    }

    .search-section {
        display: none !important;
    }

    .main-navigation {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }
}

@media (max-width: 480px) {
    .header-main {
        padding: 0.3rem 0;
        width: 100%;
    }

    .header-content {
        padding: 0 0.5rem;
        width: 100%;
    }

    .top-bar-content {
        padding: 0 0.5rem;
        width: 100%;
    }

    .logo-main {
        font-size: 1rem;
    }

    .logo-tagline {
        font-size: 0.5rem;
    }

    body {
        padding-top: 40px;
    }
}

/* Desktop Navigation Display */
@media (min-width: 769px) {
    .mobile-menu-toggle {
        display: none !important;
    }

    .main-navigation {
        display: block !important;
    }

    .search-section {
        display: block !important;
    }

    .header-top-bar {
        display: block !important;
    }

    .professional-header {
        position: fixed !important;
        width: 100% !important;
    }

    .header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }

    .nav-menu {
        display: flex !important;
        align-items: center !important;
    }

    .nav-link span {
        display: inline !important;
    }

    .user-info {
        display: flex !important;
    }
}

/* Animation for smooth transitions */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-menu {
    animation: fadeInDown 0.3s ease-out;
}

/* Hover effects */
.nav-item:hover .nav-link {
    color: var(--primary-color);
}

/* Focus states for accessibility */
.nav-link:focus,
.search-input:focus,
.search-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading state for search */
.search-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.search-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ========================================
   LOGIN MODAL ERROR HANDLING STYLES
   ======================================== */

.error-message {
    background: #fee;
    border: 1px solid #fecaca;
    border-left: 4px solid #e53e3e;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: slideInDown 0.3s ease-out;
}

.success-message {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-left: 4px solid #38a169;
    color: #2f855a;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: slideInDown 0.3s ease-out;
}

.error-message i,
.success-message i {
    font-size: 1.1rem;
    flex-shrink: 0;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading button styles */
.login-btn {
    position: relative;
    transition: all 0.3s ease;
}

.login-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.btn-loading i {
    animation: spin 1s linear infinite;
}

/* Form validation styles */
.form-group input.error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-group input.success {
    border-color: #38a169;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

/* ========================================
   DESKTOP SPECIFIC RESPONSIVE FIXES
   ======================================== */

/* Large Desktop - 1441px and up */
@media (min-width: 1441px) {
    .top-bar-content,
    .header-content {
        max-width: 1600px;
        padding: 0 3rem;
    }

    .search-section {
        max-width: 700px;
    }

    .nav-menu {
        gap: 1rem;
    }

    .nav-link {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
}

/* Standard Desktop - 1025px to 1440px */
@media (min-width: 1025px) and (max-width: 1440px) {
    .top-bar-content,
    .header-content {
        max-width: 1400px;
        padding: 0 1.5rem;
    }

    .search-section {
        max-width: 450px;
    }

    .nav-menu {
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }

    .header-main {
        padding: 0.5rem 0;
    }

    .header-top-bar {
        padding: 4px 0;
    }
}

/* Medium Desktop - 993px to 1024px */
@media (min-width: 993px) and (max-width: 1024px) {
    .top-bar-content,
    .header-content {
        padding: 0 1.5rem;
    }

    .search-section {
        max-width: 450px;
    }

    .nav-menu {
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.7rem 0.9rem;
        font-size: 0.9rem;
    }

    .contact-info {
        gap: 1.5rem;
    }

    .social-links {
        gap: 0.75rem;
    }
}



/* Fix for very large screens */
@media (min-width: 1600px) {
    .top-bar-content,
    .header-content {
        max-width: 1800px;
        padding: 0 4rem;
    }

    .search-section {
        max-width: 800px;
    }
}

/* Ensure dropdowns work properly on desktop */
@media (min-width: 993px) {
    .dropdown:hover .dropdown-menu {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
        display: block !important;
    }

    .dropdown-menu {
        position: absolute !important;
        top: 100% !important;
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid #e1e5e9 !important;
        z-index: 1000 !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Professional Header JavaScript

    // ========================================
    // RESPONSIVE HEADER MANAGEMENT
    // ========================================

    function initializeResponsiveHeader() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-nav');
        const mobileClose = document.getElementById('mobile-nav-close');

        // Mobile menu toggle
        if (mobileToggle) {
            mobileToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleMobileMenu();
            });
        }

        // Mobile menu close
        if (mobileClose) {
            mobileClose.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeMobileMenu();
            });
        }

        // Close mobile menu when clicking overlay
        if (mobileOverlay) {
            mobileOverlay.addEventListener('click', function(e) {
                if (e.target === mobileOverlay) {
                    closeMobileMenu();
                }
            });
        }

        // Close mobile menu on window resize to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMobileMenu();
            }
        });

        // Close mobile menu with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    }

    function toggleMobileMenu() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-nav');

        if (mobileOverlay && mobileToggle) {
            const isActive = mobileOverlay.classList.contains('active');

            if (isActive) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }
    }

    function openMobileMenu() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-nav');

        if (mobileOverlay && mobileToggle) {
            mobileOverlay.classList.add('active');
            mobileToggle.classList.add('active');
            mobileToggle.setAttribute('aria-expanded', 'true');
            document.body.style.overflow = 'hidden';
        }
    }

    function closeMobileMenu() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-nav');

        if (mobileOverlay && mobileToggle) {
            mobileOverlay.classList.remove('active');
            mobileToggle.classList.remove('active');
            mobileToggle.setAttribute('aria-expanded', 'false');
            document.body.style.overflow = 'auto';
        }
    }



    // Initialize responsive header
    initializeResponsiveHeader();



    // Enhanced search functionality
    const searchForm = document.querySelector('.advanced-search-form');
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');

    if (searchForm && searchInput && searchBtn) {
        // Search suggestions (can be enhanced with AJAX)
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Add search suggestions logic here
            }, 300);
        });

        // Search form submission with loading state
        searchForm.addEventListener('submit', function(e) {
            if (searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.focus();
                return;
            }

            searchBtn.classList.add('loading');
            searchBtn.disabled = true;
        });
    }

    // Dropdown menus enhancement
    const dropdowns = document.querySelectorAll('.dropdown');

    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        let hideTimeout;

        dropdown.addEventListener('mouseenter', function() {
            clearTimeout(hideTimeout);
            if (menu) {
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
            }
        });

        dropdown.addEventListener('mouseleave', function() {
            hideTimeout = setTimeout(() => {
                if (menu) {
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.transform = 'translateY(-10px)';
                }
            }, 150);
        });
    });

    // User menu specific functionality
    const userBtn = document.getElementById('user-btn');
    const userMenu = document.getElementById('user-menu');

    if (userBtn && userMenu) {
        userBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const isVisible = userMenu.style.display === 'block';

            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== userMenu) {
                    menu.style.display = 'none';
                }
            });

            userMenu.style.display = isVisible ? 'none' : 'block';
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!userBtn.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.style.display = 'none';
            }
        });
    }

    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.professional-header');

    if (header) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = '#ffffff';
                header.style.backdropFilter = 'none';
            }

            // Hide header on scroll down, show on scroll up
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
        });
    }

    // Notification badge animation
    const notificationBadge = document.querySelector('.notification-badge');
    if (notificationBadge) {
        notificationBadge.addEventListener('animationend', function() {
            this.style.animation = 'none';
        });

        // Pulse animation for new notifications
        setInterval(() => {
            if (notificationBadge.textContent !== '0') {
                notificationBadge.style.animation = 'pulse 2s infinite';
            }
        }, 5000);
    }

    // Language selector functionality
    const languageBtn = document.getElementById('language-btn');
    const languageMenu = document.getElementById('language-menu');

    if (languageBtn && languageMenu) {
        languageMenu.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                const selectedLang = e.target.textContent.trim().split(' ')[1];
                languageBtn.querySelector('span').textContent = selectedLang.substring(0, 2).toUpperCase();
            }
        });
    }

    // Mobile login/signup integration
    const mobileLoginBtn = document.getElementById('mobile-login-btn');
    const mobileSignupBtn = document.getElementById('mobile-signup-btn');
    const showLoginBtn = document.getElementById('show-login-btn');
    const showSignupBtn = document.getElementById('show-signup-btn');

    if (mobileLoginBtn && showLoginBtn) {
        mobileLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showLoginBtn.click();
            if (mobileNav) mobileNav.style.display = 'none';
            if (mobileMenuToggle) mobileMenuToggle.classList.remove('active');
        });
    }

    if (mobileSignupBtn && showSignupBtn) {
        mobileSignupBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showSignupBtn.click();
            if (mobileNav) mobileNav.style.display = 'none';
            if (mobileMenuToggle) mobileMenuToggle.classList.remove('active');
        });
    }

    // Keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close all dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });

            // Close mobile menu
            if (mobileNav) mobileNav.style.display = 'none';
            if (mobileMenuToggle) mobileMenuToggle.classList.remove('active');
        }

        // Focus search with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (searchInput) searchInput.focus();
        }
    });

    // Add pulse animation for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);

    // ========================================
    // AJAX LOGIN FUNCTIONALITY
    // ========================================

    const loginForm = document.getElementById('login-form');
    const loginError = document.getElementById('login-error');
    const loginSuccess = document.getElementById('login-success');
    const loginErrorText = document.getElementById('login-error-text');
    const loginSuccessText = document.getElementById('login-success-text');
    const loginSubmitBtn = document.getElementById('login-submit-btn');
    const btnText = loginSubmitBtn.querySelector('.btn-text');
    const btnLoading = loginSubmitBtn.querySelector('.btn-loading');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Hide previous messages
            hideMessages();

            // Show loading state
            showLoading();

            // Get form data
            const formData = new FormData(loginForm);

            // Send AJAX request
            fetch('login.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    showSuccess(data.message || 'Connexion réussie!');

                    // Redirect after success
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showError(data.message || 'Email ou mot de passe incorrect.');

                    // Add error styling to inputs
                    const emailInput = document.getElementById('login-email');
                    const passwordInput = document.getElementById('login-password');
                    emailInput.classList.add('error');
                    passwordInput.classList.add('error');

                    // Remove error styling after user starts typing
                    emailInput.addEventListener('input', function() {
                        this.classList.remove('error');
                        hideMessages();
                    });

                    passwordInput.addEventListener('input', function() {
                        this.classList.remove('error');
                        hideMessages();
                    });
                }
            })
            .catch(error => {
                hideLoading();
                showError('Une erreur est survenue. Veuillez réessayer.');
                console.error('Login error:', error);
            });
        });
    }

    function showError(message) {
        loginErrorText.textContent = message;
        loginError.style.display = 'flex';
        loginSuccess.style.display = 'none';
    }

    function showSuccess(message) {
        loginSuccessText.textContent = message;
        loginSuccess.style.display = 'flex';
        loginError.style.display = 'none';
    }

    function hideMessages() {
        loginError.style.display = 'none';
        loginSuccess.style.display = 'none';
    }

    function showLoading() {
        loginSubmitBtn.classList.add('loading');
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
    }

    function hideLoading() {
        loginSubmitBtn.classList.remove('loading');
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
});
</script>

</body>
</html>
