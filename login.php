<?php
session_start();
require_once 'db.php';
$conn = $pdo;

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: dashboard.php");
    exit;
}

$error = '';

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (!empty($email) && !empty($password)) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Login réussi
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['username'] = $user['username'];

            // Set success message
            $_SESSION['success'] = "Connexion réussie. Bienvenue " . $user['username'] . "!";

            // Return JSON response for AJAX requests
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => "Connexion réussie. Bienvenue " . $user['username'] . "!",
                    'user_type' => $user['user_type'],
                    'redirect_url' => $user['user_type'] === 'freelancer' ? 'freelancer_dashboard.php' : 'client_dashboard.php'
                ]);
                exit;
            }

            // Redirect based on user type for non-AJAX requests
            if ($user['user_type'] === 'freelancer') {
                header("Location: freelancer_dashboard.php");
            } else {
                header("Location: client_dashboard.php");
            }
            exit;
        } else {
            $error = "Email ou mot de passe incorrect.";

            // Return JSON error for AJAX requests
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => $error
                ]);
                exit;
            }
        }
    } else {
        $error = "Tous les champs sont requis.";

        // Return JSON error for AJAX requests
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $error
            ]);
            exit;
        }
    }
}

// If the page was accessed directly, show a standalone login form
if (!isset($_SERVER['HTTP_REFERER']) || strpos($_SERVER['HTTP_REFERER'], 'index.php') === false) {
    include 'includes/header.php';
?>

<div class="form-container">
    <h2>Connexion</h2>
    
    <?php if (!empty($error)): ?>
        <div class="error-message" style="background-color: #fee; color: #e53e3e; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <?= $error; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="login.php">
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">Mot de passe</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-buttons">
            <button type="submit" class="btn btn-primary">Se connecter</button>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <p>Vous n'avez pas de compte ? <a href="signup.php">S'inscrire</a></p>
            <p><a href="#">Mot de passe oublié ?</a></p>
        </div>
    </form>
</div>

<?php
    include 'includes/footer.php';
}
?>
