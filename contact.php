<?php
session_start();
require_once 'db.php';
$conn = $pdo;
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "Vous devez être connecté pour accéder à cette page.";
    header("Location: login.php");
    exit;
}

$sender_id = $_SESSION['user_id'];
$error = '';
$success = '';

// <PERSON>le sending a message
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['send_message'])) {
        $receiver_id = intval($_POST['receiver_id']);
        $message = sanitize($_POST['message']);
        
        if (empty($message)) {
            $error = "Le message ne peut pas être vide.";
        } else {
            // Insert the message into the database
            $stmt = $conn->prepare("INSERT INTO messages (sender_id, receiver_id, message, sent_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$sender_id, $receiver_id, $message]);
            
            $success = "Message envoyé avec succès.";
        }
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Envoyer un message</h1>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="mb-4">
                <label for="receiver_id" class="block text-sm font-medium text-gray-700 mb-1">Receveur</label>
                <input type="number" id="receiver_id" name="receiver_id" class="w-full px-3 py-2 border rounded-md" required>
            </div>
            
            <div class="mb-4">
                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                <textarea id="message" name="message" rows="4" class="w-full px-3 py-2 border rounded-md" required></textarea>
            </div>
            
            <button type="submit" name="send_message" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition">
                Envoyer
            </button>
        </form>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
